import React, { useEffect, useMemo, useRef, useState } from 'react';
import './index.less';
import RuleManage, { RuleManageProps } from '@components/RuleManage';
import { useDynamicWorkDesignStore } from '../../../store';
import './index.less';
import {
  RuleManageOriginData,
  ExternalAddSelectInfo,
} from '@/components/RuleManage/config/ruleManage.type';
import { globalEventEmitter, useGlobalEventEmitter } from '@/common/hooks';
import { DynamicWorkDesignEventType, PageCode } from '../../../config/type';
import { RuleData } from '../../DynamicWorkDesignContent/type';
import { SaveType } from "./../type";
// import {
//   transformInputRules,
//   transformOutRules,
// } from "./../tools/index";

export interface RuleTabProps {}
const RuleTab: React.FC<RuleTabProps> = (props: RuleTabProps) => {
  const {
    version,
    dynamicWorkDesignInfo,
    dynamicWorkDesignRenderData,
    setDynamicWorkDesignRenderDataByKey,
  } = useDynamicWorkDesignStore((state) => ({
    version: state.version,
    dynamicWorkDesignInfo: state.dynamicWorkDesignInfo,
    dynamicWorkDesignRenderData: state.dynamicWorkDesignRenderData,
    setDynamicWorkDesignRenderDataByKey: state.setDynamicWorkDesignRenderDataByKey,
  }));

  // 离开新增规则面板时，关闭新增规则面板
  useEffect(() => {
    const handleRuleAddSelectMouseLeave = (event) => {
      // 这里通过class来判断，当然也可以选择在打开新增规则面板时暴露ref，直接用该ref判断，更加精确
      if (event?.target?.classList?.contains('custom-position-add-select')) {
        if (!event.target.contains(event.relatedTarget)) {
          // 传null 就是 关闭面板
          ruleManageRef?.current?.openAddSelect(null);
        }
      }
    };
    document.addEventListener('mouseleave', handleRuleAddSelectMouseLeave, true);
    return () => {
      document.removeEventListener('mouseleave', handleRuleAddSelectMouseLeave, true);
    };
  }, []);

  const ruleManageRef = useRef(null);

  const ruleConfig: RuleManageOriginData = useMemo(() => {
    if (version !== '2.0' && !dynamicWorkDesignInfo) return null;
    const { code, category, applicationCode } = dynamicWorkDesignInfo || {};

    return {
      domainType: version === '2.0' ? '' : 'DataEntry', //TODO  数据录入先 固定 DataEntry，待 后续 梳理完整场景后 变成 配置项
      code: version === '2.0' ? '' : code,
      applicationCode: version === '2.0' ? '' : applicationCode,
      fieldTreeMap: dynamicWorkDesignRenderData?.fieldTreeMap,
      isMobile: false,
      isOffline: version === '2.0', // 2.0新版时使用离线规则
      // offlineRuleList:
      //   version === '2.0' ? transformInputRules(dynamicWorkDesignRenderData?.pageUIElementContent?.rules, {
      //     application: version === '2.0' ? '' : applicationCode,
      //     domainId: version === '2.0' ? '' : code,
      //     domain: version === '2.0' ? '' : 'DataEntry',
      //   }) : [],
      offlineRuleList:
        version === '2.0' ? dynamicWorkDesignRenderData?.pageUIElementContent?.rules : [],
      defaultDataSourceName: version === '2.0' ? '' : dynamicWorkDesignRenderData?.dataSourceName,
      hideShortcut: version === '2.0', // 隐藏复制、粘贴、模板等功能
      hideAuth: version === '2.0', // 隐藏权限控制
      // hideRules: version === '2.0' ? ['custom', 'hidden', 'connection', 'executeScript'] : []
    };
  }, [
    version,
    dynamicWorkDesignInfo,
    dynamicWorkDesignRenderData?.pageUIElementContent?.rules,
    dynamicWorkDesignRenderData?.fieldTreeMap]);

  const handleRuleListChange = (data) => {
    if (version === '2.0') {
      // 2.0中，吐出画布的整个数据data需要调整
      setDynamicWorkDesignRenderDataByKey('pageUIElementContent', {
        ...(dynamicWorkDesignRenderData?.pageUIElementContent ?? {}),
        // rules: transformOutRules(data),
        rules: data,
      });

      globalEventEmitter.emit(
        DynamicWorkDesignEventType.AthChangePageUIElementContent,
        {
          rules: data,
        },
        { isLoadImmediately: false, dataKey: 'rules', saveKey: 'useCoreData.rules', saveType: SaveType.Root }
      );
    } else {
      setDynamicWorkDesignRenderDataByKey('ruleList', data);
    }
  };

  useGlobalEventEmitter(DynamicWorkDesignEventType.LowCodeRulesHandle, (eventData: RuleData) => {
    const { type, contextDataSourceName, data } = eventData;

    if (type === 'add') {
      if (data?.addRuleBaseInfo?.content) {
        let scope = dynamicWorkDesignInfo.pageCode === PageCode.EDIT_PAGE ? 'EDIT' : '';
        // 2025-07-08 如果是新版设计器，scope置空
        if (version === '2.0') {
          scope = '';
        }
        data['addRuleBaseInfo']['content']['scope'] = scope;
      }

      ruleManageRef?.current?.openAddSelect({
        ...data,
        contextDataSourceName,
      } as ExternalAddSelectInfo);
    }
    if (type === 'delete') {
      ruleManageRef?.current?.deleteRule(data);
    }
    if (type === 'edit') {
      ruleManageRef?.current?.editRule(data, contextDataSourceName);
    }
  });

  const handleToTemplateManager = () => {
    const ruleTemplateManagerUrl =
      dynamicWorkDesignInfo?.dynamicWorkDesignConfig?.businessConfig?.ruleTemplateManagerUrl;
    if (!ruleTemplateManagerUrl) return;
    window.open(ruleTemplateManagerUrl, '_blank');
  };

  return (
    <div className="rule-tab">
      {!!ruleConfig && (
        <RuleManage
          ref={ruleManageRef}
          {...ruleConfig}
          ruleListChange={handleRuleListChange}
          toTemplateManager={handleToTemplateManager}
        />
      )}
    </div>
  );
};

export default RuleTab;
