import { useEffect } from 'react';
import {
  DynamicWorkDesignInfo,
  DynamicWorkDesignRenderData,
  DynamicWorkDesignRenderWholeData,
  DynamicWorkDesignStatus,
} from '../../config/type';
import {
  useDynamicWorkDesignStore,
  useDynamicWorkDesignContentStore,
  useDynamicWorkDesignSideBarStore,
} from '../../store';
import { DebouncedFunc } from 'lodash';
import {
  transformInputRules,
  transformOutRules
} from "@components/DynamicWorkDesign/components/DynamicWorkDesignSideBar/tools";

async function useDynamicWorkDesignInit(
  version: string,
  isPreload: boolean,
  dynamicWorkDesignInfoOrigin: DynamicWorkDesignInfo,
  dynamicWorkDesignRenderDataOrigin: DynamicWorkDesignRenderData,
  changeDynamicWorkDesignRenderDataDeBounce: DebouncedFunc<
    (dynamicWorkDesignRenderWholeData: DynamicWorkDesignRenderWholeData) => void
  >,
  dynamicWorkDesignStatus: DynamicWorkDesignStatus
) {
  const { setLowcodeRenderInitData, reset: resetDynamicWorkDesignContentStore } =
    useDynamicWorkDesignContentStore((state) => ({
      setLowcodeRenderInitData: state.setLowcodeRenderInitData,
      reset: state.reset,
    }));

  const { reset: resetDynamicWorkDesignSideBarStore } = useDynamicWorkDesignSideBarStore(
    (state) => ({
      reset: state.reset,
    })
  );

  const {
    setVersion,
    setIsPreload,
    setDynamicWorkDesignInfo,
    dynamicWorkDesignRenderData,
    setDynamicWorkDesignRenderData,
    reset: resetDynamicWorkDesignStore,
  } = useDynamicWorkDesignStore((state) => ({
    setVersion: state.setVersion,
    setIsPreload: state.setIsPreload,
    setDynamicWorkDesignInfo: state.setDynamicWorkDesignInfo,
    dynamicWorkDesignRenderData: state.dynamicWorkDesignRenderData,
    setDynamicWorkDesignRenderData: state.setDynamicWorkDesignRenderData,
    reset: state.reset,
  }));

  // 组件销毁时重置store
  useEffect(() => {
    return () => {
      resetDynamicWorkDesignStore();
      resetDynamicWorkDesignContentStore();
      resetDynamicWorkDesignSideBarStore();
    };
  }, []);

  useEffect(() => {
    if (dynamicWorkDesignStatus === DynamicWorkDesignStatus.Loading) return;
    if (version === '2.0') {
      changeDynamicWorkDesignRenderDataDeBounce({
        ...dynamicWorkDesignRenderData,
        pageUIElementContent: {
          ...(dynamicWorkDesignRenderData?.pageUIElementContent ?? {}),
          rules: transformOutRules(dynamicWorkDesignRenderData?.pageUIElementContent?.rules),
        }
      });
    } else {
      changeDynamicWorkDesignRenderDataDeBounce(dynamicWorkDesignRenderData);
    }
    // 当dynamicWorkDesignRenderData发生变化时，告知外部最新的数据
    // 由于DynamicWorkDesign还包含lowcode画布子应用
    // 为了数据流清晰，dynamicWorkDesignRenderData 中的数据 分为 pageUIElementContent（五要素） 和 除pageUIElementContent之外的其他数据 （后续如果有调整，可以在 类型定义中增加备注）
    // dynamicWorkDesignRenderData 中的 pageUIElementContent（五要素） 是lowcode 维护的
    // dynamicWorkDesignRenderData 中的 其他数据是由 DynamicWorkDesign自身维护的（维护store）
    // 正常来说，在初始化完成之后pageUIElementContent数据的一切变更都 是由 lowcode画布自己完成的
    // 当lowcode画布操作画布数据，完成pageUIElementContent数据的变更之后，通过事件告知 DynamicWorkDesign更新 store中的pageUIElementContent，完成数据同步
    // 之后 会通过changeDynamicWorkDesignRenderDataDeBounce 统一 告知 外部最新的数据
    // 但，有时 DynamicWorkDesign 也有操作pageUIElementContent的诉求，那么可以通过AthChangePageUIElementContent事件，让lowcode画布更新pageUIElementContent数据
    // 之后自动会触发之后一系列流程
    // 同理，当 lowcode画布需要 修改dynamicWorkDesignRenderData中非pageUIElementContent数据时，也是类似，通过事件让DynamicWorkDesign 去完成数据的更新
  }, [dynamicWorkDesignRenderData]);

  useEffect(() => {
    setVersion(version);
    setIsPreload(isPreload);
    // 初始化/更新渲染数据 的 过程
    // DynamicWorkDesign基础信息的配置
    setDynamicWorkDesignInfo(dynamicWorkDesignInfoOrigin);
    // 将lowcode 初始化必要数据全部清空
    // 之后 在 useLowcodeRenderInitData 里
    // 会根据 当前最新的 dynamicWorkDesignRenderData 重新组装 lowcodeRenderInitData
    // 之后就会重新触发 lowcode 画布的  init的事件，初始化 画布
    setLowcodeRenderInitData(null);
    // DynamicWorkDesign当前作业的完整数据，这份数据会会进行merge，因为有部分数据是在加载之后生成的，这部分数据需要保留
    const currentDynamicWorkDesignRenderData = {
      ...dynamicWorkDesignRenderData,
      ...dynamicWorkDesignRenderDataOrigin,
    };

    // 2.0应用中，从dataConnectors中取数据
    if (version === '2.0') {
      const newFieldTreeMap: Map<string, any> = new Map();
      const { dataConnectors = [] } = dynamicWorkDesignRenderDataOrigin?.pageUIElementContent ?? {};
      dataConnectors.map((dataSource) => {
        const name = dataSource.name;
        const fieldTree = dataSource.option?.responseTree;
        newFieldTreeMap.set(name, fieldTree ? [fieldTree] : []);
      });
      currentDynamicWorkDesignRenderData['fieldTreeMap'] = newFieldTreeMap;
      currentDynamicWorkDesignRenderData['pageUIElementContent'] = {
        ...currentDynamicWorkDesignRenderData['pageUIElementContent'],
        rules: transformInputRules(currentDynamicWorkDesignRenderData?.pageUIElementContent?.rules, {
          application: '',
          domainId: '',
          domain: ''
        })
      }
    }
    // 这里需要在组装一下
    setDynamicWorkDesignRenderData(currentDynamicWorkDesignRenderData);
  }, [version, isPreload, dynamicWorkDesignInfoOrigin, dynamicWorkDesignRenderDataOrigin]);
}

export default useDynamicWorkDesignInit;
