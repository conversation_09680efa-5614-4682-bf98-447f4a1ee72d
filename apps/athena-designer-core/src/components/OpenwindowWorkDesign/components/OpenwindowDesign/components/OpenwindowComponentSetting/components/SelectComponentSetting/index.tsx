import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
// import './index.less';
import { t } from 'i18next';
import { ATHENA_TYPES } from '@/components/OpenwindowWorkDesign/utils/fields.utils';
import { Checkbox, Form, Input, InputNumber, Select } from 'antd';
import { dslI18n, getComponentList } from '../../../../config';
import AppLangInput from '@/components/AppLangInput';
import { cloneDeep, debounce } from 'lodash';
import { useWorkData } from '@/components/OpenwindowWorkDesign/store/workData';
import CustomOptions from './CustomOptions';

const { Option } = Select;

interface SelectComponentSettingProps {
  data: any;
  //   columnType: string;
  isPrivatization: boolean;
  change: (data: any) => void;
}

const SelectComponentSetting = (props: SelectComponentSettingProps) => {
  const isInitialized = useRef(false);
  const [dataTypeOptions, setDataTypeOptions] = useState<string[]>([]); // 数据类型
  const [componentForm] = Form.useForm(); // 表单 [文本输入、日期选择、时间选择、数字输入、百分比]
  useEffect(() => {
    handleInit();
  }, [props.data]);

  /**
   * 初始化
   */
  const handleInit = () => {
    const {
      type,
      id,
      headerName,
      schema,
      path,
      placeholder,
      editable,
      isFocusDisplay,
      dataType,
      lang,
    } = props.data;
    setDataTypeOptions(getComponentList(type, dataType));
    const status = editable ? 'editable' : 'disabled';
    componentForm.setFieldsValue({
      id, // 唯一标识
      headerName: lang?.headerName ?? dslI18n, // 标题 修改标题时候，placeholder和label有联动
      schema, // schema
      path, // path
      placeholder: lang?.placeholder ?? dslI18n, // 占位提示
      status, // 状态
      isFocusDisplay, // 是否启用标题在内样式
      dataType, // 数据类型
    });
  };

  /**
   * 多语言输入框回调
   * @param key
   * @param data
   */
  const handlePatchLang = (key: any, data: any) => {
    if (['headerName'].includes(key)) {
      // 联动处理
      const placeInput = { zh_CN: '请输入', zh_TW: '請輸入', en_US: 'Please Input ' };
      const placeholderLang = {
        label: `${placeInput[t('dj-LANG')]}${data[t('dj-LANG')]}`,
        zh_CN: `${placeInput['zh_CN']}${data['zh_CN']}`,
        zh_TW: `${placeInput['zh_TW']}${data['zh_TW']}`,
        en_US: `${placeInput['en_US']}${data['en_US']}`,
      };
      componentForm.setFieldsValue({
        [key]: { ...data, label: `${data?.[t('dj-LANG')]}` },
        ['placeholder']: placeholderLang,
        ['label']: { ...data, label: `${data?.[t('dj-LANG')]}` },
      });
    } else {
      componentForm.setFieldsValue({
        [key]: { ...data, label: `${data?.[t('dj-LANG')]}` },
      });
    }
    onChange();
  };

  // 创建防抖函数并保持引用不变
  const debouncedSearch = useCallback(
    debounce((key, data) => handlePatchLang(key, data), 500),
    [] // 依赖项为空，确保防抖函数只创建一次
  );

  // 输入框变化事件
  const handleChange = (key: any, data: any) => {
    debouncedSearch(key, data); // 触发防抖函数
  };

  /**
   * 是否启用标题在内样式变更
   * @param e
   */
  const handleChangeIsFocusDisplay = (e) => {
    componentForm.setFieldsValue({ isFocusDisplay: e.target.checked });
    onChange();
  };

  /**
   * 数据类型变更
   * @param e
   */
  const handleChangeDataType = (e) => {
    componentForm.setFieldsValue({ dataType: e });
    onChange();
  };

  const handleChangeAttr = (key, value) => {
    componentForm.setFieldsValue({ [key]: value });
    onChange();
  };

  /**
   * 选项配置变更
   * @param data
   */
  const handleChangeOptions = (e: any): void => {
    const { options, dictionaryId, dictionaryKey, enumKey } = e;
    let data = {
      ...props.data,
      options,
      dictionaryId,
      dictionaryKey,
      enumKey,
    };
    props.change({
      isControl: true,
      value: data,
    });
  };

  /**
   * 回填
   */
  const onChange = () => {
    console.log(componentForm.getFieldsValue());
    const { headerName, placeholder, status, isFocusDisplay, dataType } =
      componentForm.getFieldsValue();
    const editable = status === 'editable';
    const disabled = !editable;
    let data = {
      ...props.data,
      headerName: headerName[t('dj-LANG')],
      placeholder: placeholder[t('dj-LANG')],
      label: headerName[t('dj-LANG')],
      lang: {
        headerName,
        placeholder,
        label: headerName,
      },
      editable,
      disabled,
      isFocusDisplay,
      dataType,
    };
    props.change({
      isControl: true,
      value: data,
    });
  };

  return (
    <div className="component-form">
      <Form
        className="form-info"
        form={componentForm}
        name="openwindow-setting-form"
        layout="vertical"
        labelCol={{
          span: 24,
        }}
        wrapperCol={{
          span: 24,
        }}
      >
        {/* 唯一标识 */}
        <Form.Item label={t('dj-唯一标识')} name="id">
          <Input disabled />
        </Form.Item>
        {/* 标题 */}
        <Form.Item label={t('dj-标题')} name="headerName">
          <AppLangInput
            required
            size="small"
            onChange={(value) => handleChange('headerName', value)}
          />
        </Form.Item>
        {/* schema */}
        <Form.Item label="schema" name="schema">
          <Input disabled />
        </Form.Item>
        {/* path */}
        <Form.Item label="path" name="path">
          <Input disabled />
        </Form.Item>
        {/* 占位提示 */}
        <Form.Item label={t('dj-占位提示')} name="placeholder">
          <AppLangInput
            required
            size="small"
            onChange={(value) => handleChange('placeholder', value)}
          />
        </Form.Item>
        {/* 状态 */}
        <Form.Item label={t('dj-状态')} name="status">
          <Select disabled>
            <Select.Option value="editable">{t('普通')}</Select.Option>
            <Select.Option value="disabled">{t('禁用')}</Select.Option>
          </Select>
        </Form.Item>
        {/* 是否启用标题在内样式 */}
        <Form.Item label={null} name="isFocusDisplay" valuePropName="checked">
          <Checkbox onChange={(e) => handleChangeIsFocusDisplay(e)}>
            {t('dj-是否启用标题在内样式')}
          </Checkbox>
        </Form.Item>
        {/* 配置选项 */}
        <Form.Item label={t('dj-配置选项')}>
          <CustomOptions
            options={props.data?.options ?? []}
            dictionaryId={props.data?.dictionaryId}
            dictionaryKey={props.data?.dictionaryKey}
            enumKey={props.data?.enumKey}
            dataType={props.data?.dataType}
            isPrivatization={props.isPrivatization}
            change={(e) => handleChangeOptions(e)}
          ></CustomOptions>
        </Form.Item>
        {/* 数据类型 */}
        <Form.Item label={t('dj-数据类型')} name="dataType">
          <Select onChange={(e) => handleChangeDataType(e)}>
            {dataTypeOptions.map((option) => {
              return <Select.Option value={option}>{option}</Select.Option>;
            })}
          </Select>
        </Form.Item>
      </Form>
    </div>
  );
};

export default SelectComponentSetting;
