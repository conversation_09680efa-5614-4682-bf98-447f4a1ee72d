import { isNone } from '@/components/DataSource/tools';
import { t } from 'i18next';
import { cloneDeep, isArray, isEmpty } from 'lodash';
import { v4 as uuid } from 'uuid';

// 提前处理无action时的metadataFields, 处理反转字段, 将subFields全部改为field统一
export function rebuildMetadata(data: any[]): any {
  return data.map((s) => {
    const {
      name: data_name,
      array: is_array,
      dataType: data_type,
      path,
      lang = {},
      subFields = [],
    } = s;
    const field = subFields.length > 0 ? rebuildMetadata(subFields) : [];
    return {
      ...s,
      data_name,
      is_array,
      data_type,
      fullPath: `${!!path ? path + '.' : ''}${data_name}`,
      description: lang.description || {},
      field,
    };
  });
}

// 词库字段
export const taskProcessFields = [
  {
    data_type: 'string',
    data_name: 'activity__performerName',
    description: {
      zh_CN: '执行者名称',
      zh_TW: '執行者名稱',
      en_US: 'Performer name',
    },
  },
  {
    data_type: 'string',
    data_name: 'activity__backLog__data',
    description: {
      zh_CN: '任务进展状态',
      zh_TW: '任務進展狀態',
      en_US: 'Mission status',
    },
  },
  {
    data_type: 'string',
    data_name: 'activity__planEndTime',
    description: {
      zh_CN: '计划完成日期',
      zh_TW: '計劃完成日期',
      en_US: 'Planned completion date',
    },
  },
  {
    data_type: 'string',
    data_name: 'activity__approval__state',
    description: {
      zh_CN: '签核',
      zh_TW: '簽核',
      en_US: 'approval state',
    },
  },
  {
    data_type: 'string',
    data_name: 'activity__data__status',
    description: {
      zh_CN: '流转状态',
      zh_TW: '流轉狀態',
      en_US: 'Flow Status',
    },
  },
  {
    data_type: 'datetime',
    data_name: 'activity__startTime',
    description: {
      zh_CN: '实际开始日期',
      zh_TW: '任務開始時間',
      en_US: 'task start time',
    },
  },
  {
    data_type: 'datetime',
    data_name: 'activity__endTime',
    description: {
      zh_CN: '实际完成日期',
      zh_TW: '任務結束時間',
      en_US: 'task end time',
    },
  },
];

// 数据源无actionId时, 讲字段中的path全部提一层
export function rebuildPath(data: any[], parent: any): any {
  return data.map((s) => {
    const { path = '', fullPath = '', field: child = [] } = s;
    const field = child.length > 0 ? rebuildPath(child, parent) : [];
    return {
      ...s,
      path: `${parent}${!!path ? '.' + path : ''}`,
      fullPath: `${parent}${!!fullPath ? '.' + fullPath : ''}`,
      field,
    };
  });
}

/**
 * 存储data_name的唯一id
 */
let reflectId = {};

/**
 * 拉平数据
 * flag: 子级数据名
 * parentId: 父级id
 */
const flatTargetArray = (origin = [], flag = 'field', parentId: any): any => {
  const final = [];
  origin.forEach((d) => {
    const _parentId = !!parentId ? parentId : null;
    if (!reflectId[d.fullPath]) {
      reflectId[d.fullPath] = `${uuid()}_${d.fullPath}`;
    }
    final.push({
      ...d,
      _parentId,
      _frontId: reflectId[d.fullPath],
    });
    if (d[flag]?.length > 0) {
      const extra = flatTargetArray(d[flag], flag, reflectId[d.fullPath]);
      final.push(...extra);
    }
  });
  return final;
};

/**
 * 拉平替换数据, covers(元数据)不再具有第一层, 全部从M以下设置, 即直接加入M的子级
 */
export function flatMetaData(origin: any, covers = []): any {
  if (covers.length === 0) {
    return {
      data: origin,
      mark: 'field',
    };
  }
  if ((!origin['field'] || origin['field']?.length === 0) && covers.length > 0) {
    return {
      data: { ...origin, field: covers },
      mark: 'field',
    };
  }
  reflectId = {};
  const originData = flatTargetArray([origin], 'field', null);
  const flatMatch = (data = [], parentId?: any): any => {
    data.forEach((d) => {
      const _parentId = !!parentId ? parentId : null;
      if (!reflectId[d.fullPath]) {
        reflectId[d.fullPath] = `${uuid()}_${d.fullPath}`;
      }
      const index = originData.findIndex((s) => s['fullPath'] === d['fullPath']);
      if (index !== -1) {
        Object.assign(originData[index], {
          ...d,
          _parentId,
          _frontId: reflectId[d.fullPath],
        });
      } else {
        originData.push({
          ...d,
          _parentId,
          _frontId: reflectId[d.fullPath],
        });
      }
      if (d['field']?.length > 0) {
        flatMatch(d['field'], reflectId[d.fullPath]);
      }
    });
  };
  flatMatch(covers, reflectId[origin.data_name]);
  return {
    data: generateFields(originData),
    mark: 'field',
  };
}

/**
 * 递归数据
 */
export const generateFields = (params: any): any => {
  const result = [];
  for (const param of params) {
    if (param._parentId === null || !param._parentId) {
      ['field', 'subFields'].forEach((s) => {
        if (Reflect.has(param, s)) {
          Reflect.deleteProperty(param, s);
        }
      });
      // 判断是否为顶层节点
      param['field'] = getChildren(param['_frontId'], params); // 获取子节点
      result.push(param);
    }
  }
  return result[0];
};

/**
 * 获取子类数据
 */
export const getChildren = (_frontId: any, array: any): any => {
  const child = [];
  for (const arr of array) {
    // 循环获取子节点
    if (arr._parentId === _frontId) {
      ['field', 'subFields'].forEach((s) => {
        if (Reflect.has(arr, s)) {
          Reflect.deleteProperty(arr, s);
        }
      });
      child.push(arr);
    }
  }
  for (const c of child) {
    // 获取子节点的子节点
    const currentChild = getChildren(c['_frontId'], array);
    if (currentChild.length > 0) {
      c['field'] = currentChild;
    }
  }
  return child;
};

/**
 * 将字段源数据对象（obj）转成前端使用标准的树结构（list）
 * @param data 字段源数据 (名称: 'data_name', 类型: 'data_type')
 * @param mark 字段子节点标识 （例: 'field'）
 * @param fieldMode 字段模式
 * @param dataSources 数据源对象
 * @param dataSourceName [FieldSourceModeEnum.Multiple，FieldSourceModeEnum.Single].includes(fieldMode) 时，当前的数据源名称
 * @param actionId FieldSourceModeEnum.Action === fieldMode 时，当前的API
 * @returns 树标准字段（list）
 */
export function formatField(
  data: any,
  mark: string,
  dataSources: any,
  dataSourceName: string
): any[] {
  if (data === null || mark === null) {
    return [];
  }
  const format = (origin, parent, root, target, parentCategory) => {
    return (origin || []).map((d) => {
      const currentTarget = target ? `${target}.${parent}` : parent;
      const fullPath = !target
        ? `${root}.${d.data_name}`
        : `${root}.${currentTarget}.${d.data_name}`;
      const returnVal = {
        ...d,
        fieldId: d.data_name,
        category: d.is_array ? 'array' : d.data_type,
        key: d.data_name,
        title: d.data_name,
        parentId: parent,
        target: currentTarget,
        fullPath: !!d.fullPath ? d.fullPath : fullPath,
        root,
        parentCategory,
        path: currentTarget, // 添加path，舍弃target
        dbFieldType: d.dbFieldType,
      };
      if (d[mark]?.length > 0) {
        returnVal['children'] = format(
          d[mark],
          d.data_name,
          root,
          currentTarget,
          returnVal['category']
        );
        returnVal['expanded'] = true;
      } else {
        returnVal['isLeaf'] = true;
      }
      returnVal['defaultFormioDataType'] = getDefaultFormioDataTypeByFormioCompType(
        getComponentType(returnVal),
        returnVal
      );
      return returnVal;
    });
  };
  const originDataSource: any = dataSources?.[dataSourceName];
  const notArray = ['true', true].includes(originDataSource?.notArray);
  const appData = cloneDeep([data]);
  appData.forEach((d) => {
    d.key = d.data_name;
    d.title = d.data_name;
    d.fieldId = d.data_name;
    d.category = d?.is_array && !notArray ? 'array' : d?.data_type;
    d.target = '';
    d['fullPath'] = !!d.fullPath ? d.fullPath : d.data_name;
    d['userTarget'] = dataSourceName;
    d.expanded = true;
    d.parentCategory = '';
    d.path = ''; // 添加path，舍弃target
    d.children = format(d[mark], d['data_name'], d['data_name'], '', d.category);
  });
  return appData;
}

export function getComponentType(data: any): string {
  if (data.isQuoteField === true || data.isQuoteField === 'true') {
    return ATHENA_TYPES.OPERATION_EDITOR;
  }
  switch (data.data_name) {
    case 'remark': {
      return ATHENA_TYPES.TEXTAREA;
    }
    case 'eoc_company_id':
    case 'eoc_site_id':
    case 'eoc_region_id': {
      return ATHENA_TYPES.EOC_SELECT;
    }
  }
  switch (data.data_type) {
    case 'string': {
      return ATHENA_TYPES.INPUT;
    }
    case 'boolean': {
      return ATHENA_TYPES.CHECKBOX;
    }
    case 'numeric':
    case 'number': {
      return ATHENA_TYPES.INPUT_NUMBER;
    }
    case 'time': {
      return ATHENA_TYPES.TIMEPICKER;
    }
    case 'date':
    case 'datetime': {
      return ATHENA_TYPES.DATEPICKER;
    }
    // case 'json': {
    //   return data.is_array === true || data.is_array === 'true' ? ATHENA_TYPES.FILE_UPLOAD : ATHENA_TYPES.FORM_UPLOAD;
    // }
    case 'file': {
      // 附件组件，用is_array来判断是否用表格或表单附件
      return data.is_array === true || data.is_array === 'true'
        ? ATHENA_TYPES.FILE_UPLOAD
        : ATHENA_TYPES.FORM_UPLOAD;
    }
    case 'multiple': {
      // 多选组件
      return ATHENA_TYPES.SELECT_MULTIPLE;
    }
  }
  return ATHENA_TYPES.CUSTOM;
}

// 根据formio组件注册的类型来获取该组件默认的dataType (options.schema.customOptions.dateType)
// 这里做默认的映射，它的上游是getComponentType()
export function getDefaultFormioDataTypeByFormioCompType(
  formioCompType: string,
  fieldItem: any
): AthenaDataType {
  switch (formioCompType) {
    case 'OPERATION_EDITOR':
    case ATHENA_TYPES.TEXTAREA:
    case ATHENA_TYPES.EOC_SELECT:
    case ATHENA_TYPES.INPUT:
    case ATHENA_TYPES.NAME_CODE_COMPONENT:
      return AthenaDataType.STRING;
    case ATHENA_TYPES.CHECKBOX:
      return AthenaDataType.BOOLEAN;
    case ATHENA_TYPES.INPUT_NUMBER:
      return AthenaDataType.NUMERIC;
    case ATHENA_TYPES.DATEPICKER:
      // 根据后台的data_type 来给DATEPICKER （支持data <AthenaDataType.DATE>:datatime<AthenaDataType.DATETIME>）组件赋不同的默认值
      return fieldItem.data_type === 'datetime' ? AthenaDataType.DATETIME : AthenaDataType.DATE;
    case ATHENA_TYPES.TIMEPICKER:
      return AthenaDataType.TIME;
    case 'athena-table':
    case ATHENA_TYPES.FORM_OPERATION_EDITOR:
    case ATHENA_TYPES.SELECT_MULTIPLE:
      return AthenaDataType.ARRAY;
    case 'form-list':
      return AthenaDataType.OBJECT;
    default:
      return AthenaDataType.STRING;
  }
}

/**
 * AthenaType
 */
export const enum AthenaDataType {
  STRING = 'string',
  NUMERIC = 'numeric',
  DATE = 'date',
  BOOLEAN = 'boolean',
  OBJECT = 'object',
  ARRAY = 'array',
  TIME = 'time',
  DATETIME = 'datetime',
}

// 设计时自用组件: 非标准组件
export enum ATHENA_TYPES_PC_OWN {
  COMMON = 'COMMON',
  INPUT_NUMBER = 'INPUT_NUMBER',
  SWITCH = 'SWITCH',
  CUSTOM = 'CUSTOM',
  TAB_EDIT = 'TAB_EDIT',
  MANAGE_STATUS = 'MANAGE_STATUS',
  BUTTON_GROUP_LIST = 'BUTTON_GROUP_LIST',
  TABLE_GROUP = 'TABLE_GROUP',
  COLLAPSE_EDIT = 'COLLAPSE_EDIT',
  ISV_CUSTOM = 'ISV_CUSTOM', // ISV 自定义组件
}

// 标准组件: 已完成
enum ATHENA_TYPES_PC {
  /* 移动端临时 */
  DW_INPUT_SWITCH = 'DW_INPUT_SWITCH',
  DW_CUSTOMIZE = 'DW_CUSTOMIZE',
  /* 标准组件: 已完成 */
  ACTIVITY_DESCRIPTION = 'ACTIVITY_DESCRIPTION',
  ACTIVITY_TITLE = 'ACTIVITY_TITLE',
  ADDRESS = 'ADDRESS',
  ADD_DOCUMENTID_CONTROL = 'ADD_DOCUMENTID_CONTROL',
  AMOUNT_INPUT = 'AMOUNT_INPUT',
  APPROVAL_DESCRIPTION = 'APPROVAL_DESCRIPTION',
  ATHENA_TABLE = 'ATHENA_TABLE',
  ATH_TAG = 'ATH_TAG', // 标签类控件
  BUSINESS_SUPPLIER = 'BUSINESS_SUPPLIER',
  BUTTON = 'BUTTON',
  CHECKBOX = 'CHECKBOX',
  CHECK_RESULT = 'CHECK_RESULT',
  COLLAPSE = 'COLLAPSE',
  COMBINATION_CHART = 'COMBINATION_CHART',
  CONTACT = 'CONTACT',
  CONTENT_QUERY_BUTTON = 'CONTENT_QUERY_BUTTON',
  CONTENT_TITLE = 'CONTENT_TITLE',
  COUNT_DOWN = 'COUNT_DOWN',
  CURRENT_ACCOUNT = 'CURRENT_ACCOUNT',
  DATEPICKER = 'DATEPICKER',
  DATE_RANGE = 'DATE_RANGE',
  DELIVERY_REPLY_DESCRIPTION = 'DELIVERY_REPLY_DESCRIPTION',
  DELIVERY_REPLY_TITLE = 'DELIVERY_REPLY_TITLE',
  DIFFERENCE_CALCULATION = 'DIFFERENCE_CALCULATION',
  DYNAMIC_GRAPH_VIEWER = 'DYNAMIC_GRAPH_VIEWER',
  EOC_MULTI_SELECT = 'EOC_MULTI_SELECT',
  EOC_SELECT = 'EOC_SELECT',
  EOC_USER_SELECT = 'EOC_USER_SELECT',
  FILE_UPLOAD = 'FILE_UPLOAD',
  FLEXIBLE_BOX = 'FLEXIBLE_BOX', // 行布局
  FORM_LIST = 'FORM_LIST',
  MODAL = 'MODAL',
  FORM_OPERATION_EDITOR = 'FORM_OPERATION_EDITOR',
  FORM_UPLOAD = 'FORM_UPLOAD',
  GRIDSTER = 'GRIDSTER',
  INPUT = 'INPUT',
  LABEL = 'LABEL',
  LAYOUT = 'LAYOUT',
  MEASURE = 'MEASURE',
  NAME_CODE_COMPONENT = 'NAME_CODE_COMPONENT',
  NEW_OLD_COMPONENT = 'NEW_OLD_COMPONENT',
  OPERATION_EDITOR = 'OPERATION_EDITOR',
  PERCENT_INPUT = 'PERCENT_INPUT',
  PLAN_SELECT = 'PLAN_SELECT',
  RADIO_GROUP = 'RADIO_GROUP',
  SELECT = 'SELECT',
  SELECT_MULTIPLE = 'SELECT_MULTIPLE',
  SIGN_OFF_PROGRESS = 'SIGN_OFF_PROGRESS',
  SIGN_OFF_PROGRESS_LINK = 'SIGN_OFF_PROGRESS_LINK',
  PERSON_SELECT = 'PERSON_SELECT',
  TABS = 'TABS',
  TASK_PROGRESS_STATUS = 'TASK_PROGRESS_STATUS',
  TEXTAREA = 'TEXTAREA',
  TEXTAREA_TEXT = 'TEXTAREA_TEXT', // 已实现未注册
  TIMEPICKER = 'TIMEPICKER',
  TIME_RANGE = 'TIME_RANGE',
  TOOLBAR = 'TOOLBAR',
  TREEDATA = 'TREEDATA',
  WORKFLOW_PROGRESS = 'WORKFLOW_PROGRESS',

  COLLAPSE_EDIT = 'COLLAPSE_EDIT',
  ISV_CUSTOM = 'ISV_CUSTOM', // ISV 自定义组件

  DRAWER = 'DRAWER', // demo
  DRAWER_ITEM = 'DRAWER_ITEM',
  DRAWER_EDIT = 'DRAWER_EDIT',
  // 标签模版打印按钮
  BUTTON_PRINT = 'BUTTON_PRINT',
}

// 标准组件: 未完成
enum ATHENA_TYPES_PC_TODO {
  ATTACHMENT = 'ATTACHMENT',
  CARD = 'CARD',
  CASE_SELECT = 'CASE_SELECT',
  CONTENT_SUBTITLE = 'CONTENT_SUBTITLE',
  COllAPSE_AND_EXPAND = 'COllAPSE_AND_EXPAND',
  DATA_FLOW_STATUS = 'DATA_FLOW_STATUS',
  DATA_UNIFORMITY_HISTORY = 'DATA_UNIFORMITY_HISTORY',
  DESCRIPTION = 'DESCRIPTION',
  DRAWER_BUTTON = 'DRAWER_BUTTON',
  DYNAMIC_GRAPH_VIEWER_EDITOR = 'DYNAMIC_GRAPH_VIEWER_EDITOR',
  ECHARTS = 'ECHARTS',
  EOC_SINGLE_SELECT = 'EOC_SINGLE_SELECT',
  FLOW = 'FLOW',
  GROUP_INLINE = 'GROUP_INLINE',
  HOVERLABEL = 'HOVERLABEL',
  IMPORTANCE_CHECK = 'IMPORTANCE_CHECK',
  LINE_TABLE = 'LINE_TABLE',
  LIST = 'LIST',
  MANUAL_CREATE_TASK = 'MANUAL_CREATE_TASK',
  MEASURE_INPUT = 'MEASURE_INPUT',
  MULTI_EOC_SELECT = 'MULTI_EOC_SELECT',
  MULTI_FIELD_LABEL = 'MULTI_FIELD_LABEL',
  NAVIGATE_REPORT = 'NAVIGATE_REPORT',
  NAVIGATE_TASK = 'NAVIGATE_TASK',
  NAVIGATE_TO_TASK = 'NAVIGATE_TO_TASK',
  PANEL = 'PANEL',
  PERSON_IN_CHARGE_SELECT = 'PERSON_IN_CHARGE_SELECT',
  PLACEHOLDER = 'PLACEHOLDER',
  PROGRAMME_PROGRESS = 'PROGRAMME_PROGRESS',
  PROGRESSBAR = 'PROGRESSBAR',
  PROJECT_TABLE = 'PROJECT_TABLE',
  QR_CODE = 'QR_CODE',
  READ_TIMES = 'READ_TIMES',
  SCANNER_GUN_ANALYSIS = 'SCANNER_GUN_ANALYSIS',
  SELECT_CONFIGURABLE = 'SELECT_CONFIGURABLE',
  SLIDER = 'SLIDER',
  SPLIT_ROW = 'SPLIT_ROW',
  STATISTIC = 'STATISTIC',
  TASK_DISTRIBUTION_EXCEPTION_HANDLING = 'TASK_DISTRIBUTION_EXCEPTION_HANDLING',
  TASK_FILE_UPLOAD = 'TASK_FILE_UPLOAD',
  TASK_FORM_UPLOAD = 'TASK_FORM_UPLOAD',
  THUMBNAIL = 'THUMBNAIL',
  UPLOAD_INVOICE = 'UPLOAD_INVOICE',
  WBS_CARDS = 'WBS_CARDS',
}

/**
 * AthenaType
 * 移动端组件类型
 */
// eslint-disable-next-line no-shadow
export enum ATHENA_TYPES_MOBILE {
  DW_GRID_GROUP = 'DW_GRID_GROUP',
  DW_ATTACHMENT = 'DW_ATTACHMENT',
  DW_PHONE_NUMBER = 'DW_PHONE_NUMBER',
  DW_EMAIL = 'DW_EMAIL',
  DW_ID_CARD = 'DW_ID_CARD',
  DW_BANK_CARD = 'DW_BANK_CARD',
  DW_INVOICE_INFO = 'DW_INVOICE_INFO',
  DW_TRAIN_TICKETS = 'DW_TRAIN_TICKETS',
  DW_APP_ENTRANCE = 'DW_APP_ENTRANCE',
  DW_PICTURE = 'DW_PICTURE',
  SCAN = 'SCAN',
  DW_INPUT_SCAN = 'DW_INPUT_SCAN',
  DW_WEBVIEW = 'DW_WEBVIEW',
  MOBILE_LABEL = 'MOBILE_LABEL',
  LOCATION = 'LOCATION',
  DRAWER = 'DRAWER',
  DRAWER_ITEM = 'DRAWER_ITEM',
  DW_DIVIDER = 'DW_DIVIDER',
  DW_EMPLOYEE_LIST = 'DW_EMPLOYEE_LIST',
  DW_GRAPHIC_DISPLAY = 'DW_GRAPHIC_DISPLAY',
  DW_QR_CODE = 'DW_QR_CODE',
  DW_TABLE = 'DW_TABLE',
  DW_TEXT_MULTI = 'DW_TEXT_MULTI',
  DW_HORIZONTAL_PROGRESS = 'DW_HORIZONTAL_PROGRESS',
  DW_PROGRESS_RATE = 'DW_PROGRESS_RATE',
  DW_PERSONNEL_GROUP = 'DW_PERSONNEL_GROUP',
  DW_PAGE_BUTTON = 'DW_PAGE_BUTTON',
  DW_INPUT_SIGN = 'DW_INPUT_SIGN',
  DW_INPUT_SCAN_MULTI = 'DW_INPUT_SCAN_MULTI',
  DW_BUTTON_SINGLE_SELECT = 'DW_BUTTON_SINGLE_SELECT',
  DW_SINGLE_SELECT = 'DW_SINGLE_SELECT',
  DW_INPUT_CASCADE_DISTRICT = 'DW_INPUT_CASCADE_DISTRICT',
  DW_INPUT_CALENDAR = 'DW_INPUT_CALENDAR',
  DW_UNI_SEARCH = 'DW_UNI_SEARCH',
  DW_BUTTON = 'DW_BUTTON',
  DW_BUTTON_JUMP = 'DW_BUTTON_JUMP',
  DW_BUTTON_SAVE = 'DW_BUTTON_SAVE',
  DW_BUTTON_CANCEL = 'DW_BUTTON_CANCEL',
  DW_BUTTON_RESET = 'DW_BUTTON_RESET',
  DW_BUTTON_STOP = 'DW_BUTTON_STOP',
  DW_BUTTON_SUBMIT = 'DW_BUTTON_SUBMIT',
  DW_SWIPER = 'DW_SWIPER',
  DW_NUMBER_LIST = 'DW_NUMBER_LIST',
  DW_ADDER_SUBSTRACTER = 'DW_ADDER_SUBSTRACTER',
  DW_ARTICLE_TITLE = 'DW_ARTICLE_TITLE',
  DW_EMPTY = 'DW_EMPTY',
  DW_WIFI = 'DW_WIFI',
  DW_GEO_LOCATION = 'DW_GEO_LOCATION',
  DW_CARD = 'DW_CARD',
  DW_ZTB_VIEW_LIST = 'DW_ZTB_VIEW_LIST',
  DW_GROUP_CARD = 'DW_GROUP_CARD',
  DW_PAGE_DESC = 'DW_PAGE_DESC',
  DW_PERSONNEL_STATE = 'DW_PERSONNEL_STATE',
  DW_MESSAGE_BAR = 'DW_MESSAGE_BAR',
  DW_PHASE_LIST = 'DW_PHASE_LIST',
  DW_INPUT_OCR = 'DW_INPUT_OCR',
  DW_DRIVE_TIPS = 'DW_DRIVE_TIPS',
  DW_GROUP_SINGLE_SELECT = 'DW_GROUP_SINGLE_SELECT',
  DW_BUTTON_GROUP = 'DW_BUTTON_GROUP',
  DW_CUSTOM_BUTTON = 'DW_CUSTOM_BUTTON',
  DW_SUBMIT_BUTTON = 'DW_SUBMIT_BUTTON',
  DW_BLANK_AREA = 'DW_BLANK_AREA',
  DW_PERSONNEL_STATE_GROUP = 'DW_PERSONNEL_STATE_GROUP',
  DW_CARD_ONE = 'DW_CARD_ONE',
  DW_CARD_LIST = 'DW_CARD_LIST',
  DW_TASK_CRUMBS = 'DW_TASK_CRUMBS',
  DW_TABS_ITEM = 'DW_TABS_ITEM',
  DW_INDEX_NAVBAR = 'DW_INDEX_NAVBAR',
  DW_CHOOSE_TASK_PROGRESS = 'DW_CHOOSE_TASK_PROGRESS',
  DW_CUSTOM_GROUP = 'DW_CUSTOM_GROUP',
  DW_SET_UP_FORM = 'set-up-form',
  DW_AUTO_FILL = 'DW_AUTO_FILL',
  DW_APPOSITION = 'DW_APPOSITION',
  DW_TABS = 'DW_TABS',
  DW_STANDARD_TABS = 'DW_STANDARD_TABS',
  DW_CUSTOM_TAB_CONTROLLER = 'DW_CUSTOM_TAB_CONTROLLER',
  DW_WEBVIEW_PANEL = 'DW_WEBVIEW_PANEL',
  DW_PANEL = 'DW_PANEL',
  DW_TAB_SCROLL_CONTAINER = 'DW_TAB_SCROLL_CONTAINER',
  DW_MULTI_FUNCTION_LIST = 'DW_MULTI_FUNCTION_LIST',
  DW_TASK_LIST = 'DW_TASK_LIST',
  DW_SINGLE_LIST_CONTAINER = 'DW_SINGLE_LIST_CONTAINER',
  DW_HEAD_BODY_CONTAINER = 'DW_HEAD_BODY_CONTAINER',
  DW_SINGLE_OPERATION_EDITOR = 'DW_SINGLE_OPERATION_EDITOR',
  'set-up-page' = 'set-up-page',
  'set-up-drawer' = 'set-up-drawer',
  'single-main-form' = 'single-main-form',
  DW_TASK_PROGRESS_LIST = 'DW_TASK_PROGRESS_LIST',
  DW_WORKFLOW_PROGRESS = 'DW_WORKFLOW_PROGRESS',
  DW_RETRACYABLE = 'DW_RETRACYABLE',
  DW_PROGRESS_BAR = 'DW_PROGRESS_BAR',
  DW_CHART_INDICATOR = 'DW_CHART_INDICATOR',
  DW_CHART_SUBPROGRESS = 'DW_CHART_SUBPROGRESS',
  DW_CHART_PIE = 'DW_CHART_PIE',
  DW_CHART_BAR = 'DW_CHART_BAR',
  DW_CHART_LINE = 'DW_CHART_LINE',
  DW_CHART_DASHBOARD = 'DW_CHART_DASHBOARD',
  DW_CHART_RADAR = 'DW_CHART_RADAR',
  DW_LIST_CONTAINER = 'DW_LIST_CONTAINER',
  DW_LINEAR_LAYOUT = 'DW_LINEAR_LAYOUT',
  DW_AVATAR = 'DW_AVATAR',
}

export const ATHENA_TYPES = {
  ...ATHENA_TYPES_PC_OWN,
  ...ATHENA_TYPES_PC,
  ...ATHENA_TYPES_PC_TODO,
  ...ATHENA_TYPES_MOBILE,
};

/**
 * 格式化树控件字段
 * @param params
 * @returns
 */
export const formatNodes = (params: any): any => {
  let { arr, prefix, layer = 0 } = params;
  arr = arr ?? [];
  if (!isArray(arr)) {
    arr = [arr];
  }
  const results = [];
  arr.forEach((element, index) => {
    const node = {
      // value: element.data_name,
      value: element.fullPath,
      label: element.data_name + '(' + element.description?.[t('dj-LANG')] + ')',
      // label: `${element.data_name} < ${element.data_name}(${element.description[t('dj-LANG')]}), ${element.data_type}${element.required === 'true' ? ', required' : ''}${element.is_array ? ', is_array' : ''}${element.is_businesskey ? ', is_businesskey' : ''}${element.is_datakey ? ', is_datakey' : ''} > `,
    };
    if (element.data_type === 'object') {
      node['children'] = formatNodes({
        arr: element.children,
        prefix: isEmpty(prefix) ? element.data_name : prefix + '.' + element.data_name,
        layer: layer + 1,
      });
    }
    results.push(node);
  });
  return results;
};
