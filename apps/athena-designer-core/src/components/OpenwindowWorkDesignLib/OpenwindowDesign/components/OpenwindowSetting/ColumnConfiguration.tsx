import { Icon } from '@/components';
import { useWorkData } from '@/components/OpenwindowWorkDesignLib/store/workData';
import { DeleteOutlined, RightOutlined } from '@ant-design/icons';
import { But<PERSON>, Modal, Popconfirm } from 'antd';
import { t } from 'i18next';
import React, { useEffect, useState } from 'react';
import './index.less';
import { cloneDeep } from 'lodash';

interface ColumnConfigurationProps {
  clickComponent: (index: number) => void;
}

/**
 * 列配置
 * @param props
 * @returns
 */
const ColumnConfiguration: React.FC<ColumnConfigurationProps> = React.memo(
  (props: ColumnConfigurationProps) => {
    const { columnDefs, updateColumnDefs, fieldTreeMap } = useWorkData((state) => state); // 列配置

    const [draggableList, setDraggableList] = useState<any[]>([]);
    const [draggedItem, setDraggedItem] = useState<any>(null);
    const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

    const handleDragStart = (e, index) => {
      e.stopPropagation();
      const items = cloneDeep(columnDefs() ?? []);
      setDraggableList(items);
      setDraggedItem(items[index]);
      setDraggedIndex(index);
      e.dataTransfer.setData('text/plain', JSON.stringify({ key: 'column', value: items[index] }));
      e.dataTransfer.effectAllowed = 'move';
      e.target.classList.add('dragging');
    };

    const handleDragEnd = (e) => {
      e.preventDefault();
      e.stopPropagation();
      e.target.classList.remove('dragging');
      console.log('onDragEnd', JSON.parse(e.dataTransfer.getData('text/plain') || '{}'));

      updateColumnDefs(draggableList);
      setDraggedItem(null);
      setDraggedIndex(null);
      setDraggableList([]);
    };

    const handleDragOver = (e, index) => {
      e.preventDefault();
      e.stopPropagation();

      if (draggedItem === null || draggedIndex === null) return;

      const draggedOverItem = draggableList[index];
      if (draggedItem === draggedOverItem) return;

      const newItems = [...draggableList];
      newItems.splice(draggedIndex, 1);
      newItems.splice(index, 0, draggedItem);
      setDraggableList(newItems);
      // 更新当前拖拽项的索引
      setDraggedIndex(index);
    };

    const onDelete = (e: any, index: number) => {
      e.stopPropagation();
      const newColumnDefs = cloneDeep(columnDefs());
      newColumnDefs.splice(index, 1);
      updateColumnDefs(newColumnDefs);
    };

    const handleClick = (e: any, index: number) => {
      e.stopPropagation();
      props.clickComponent(index);
    };

    return (
      <>
        <div
          style={{
            width: '100%',
            minHeight: '52px',
            maxHeight: '252px',
            border: '1px #E6E6EB solid',
            borderRadius: '4px',
            overflowY: 'auto',
          }}
        >
          <div style={{ padding: '8px' }}>
            {(columnDefs() ?? [])?.length > 0 ? (
              (columnDefs() ?? []).map((def, i) => {
                return (
                  <div
                    className="column-condition-item"
                    draggable
                    onDragStart={(e) => handleDragStart(e, i)}
                    onDragEnd={(e) => handleDragEnd(e)}
                    onDragOver={(e) => handleDragOver(e, i)}
                    key={def.id}
                    onClick={(e) => handleClick(e, i)}
                  >
                    <div className="operate">
                      <a>
                        <Icon className="iconfont button-move" type="icontuozhuai1" />
                      </a>
                    </div>
                    <div className="header-name">{def?.lang?.headerName?.[t('dj-LANG')]}</div>
                    <div className="operate other-operate">
                      <Icon
                        className="iconfont"
                        type="iconbackMenu"
                        style={{ cursor: 'auto !important', fontSize: '13px' }}
                      />

                      {/* <Popconfirm
                        className="confirm-delete"
                        placement="top"
                        title={t('dj-确认删除吗？')}
                        onConfirm={(e) => {
                          onDelete(e, i)
                        }}
                        onCancel={() => {}}
                        okText={t('dj-删除')}
                        cancelText={t('dj-取消')}
                      > */}
                      <Icon
                        className="iconfont"
                        type="iconshanchu3"
                        style={{ marginLeft: '8px', fontSize: '13px' }}
                        onClick={(e) => onDelete(e, i)}
                      />
                      {/* </Popconfirm> */}
                    </div>
                  </div>
                );
              })
            ) : (
              <div
                style={{
                  height: '36px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                {t('dj-暂无内容')}
              </div>
            )}
          </div>
        </div>
      </>
    );
  }
);

export default ColumnConfiguration;
