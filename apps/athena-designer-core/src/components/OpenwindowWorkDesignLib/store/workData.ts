import { cloneDeep, isEmpty } from 'lodash';
import { create } from 'zustand';
import { combinLayout } from '../OpenwindowDesign/config';
import { handleCombinSearchInfosTree } from '../utils/fields.utils';

interface WorkData {
  workData: any;
  cloneActiveData: any;
  activeData: () => any;
  openWindowDefine: () => any;
  columnType: () => string;
  dataConnectors: () => any[];
  // dataConnectorId: () => string;
  fieldTreeMap: () => Map<string, any>;
  fields: () => any[];
  treeData: () => any[];
  initWorkData: (data: any) => void;
  updateActiveData: (data: any) => void;
  updateOpenWindowDefine: (data: any) => void;
  updateFields: (data: any) => void;
  searchInfosTree: any[]; // 高级搜索字段列表
  searchInfosType: Map<any, any>;
  // 列配置
  columnDefs: () => any;
  updateColumnDefs: (data: any) => void;
}

export const useWorkData = create<WorkData>((set, get) => ({
  workData: {},
  cloneActiveData: {},
  activeData: () => get().workData?.data ?? {},
  openWindowDefine: () => get().workData?.data?.openWindowDefine ?? {},
  columnType: () => get().workData?.component?.type,
  dataConnectors: () => get().workData?.dataConnectors ?? [],
  // dataConnectorId: () => get().workData?.dataConnectorId,
  fieldTreeMap: () => get().workData?.fieldTreeMap,
  fields: () => get().workData?.fields ?? [],
  treeData: () => get().workData?.treeData ?? [],
  /**
   * 初始化工作数据，格式化并设置默认值
   * @param data - 输入的原始数据对象
   */
  initWorkData: (data: any) => {
    const columnType = data.component?.type;
    // 解构数据，分离 attach 和 openWindowDefine 字段
    const { attach, openWindowDefine, condition, description, lang, navigationConfig } =
      data?.data ?? {};

    // 构建新的 activeData 对象，包含默认值
    let newActiveData = cloneDeep({
      ...(data?.data ?? {}),
      attach: {
        mode: attach?.mode ?? 'row', // 默认模式为 row
        applyToField: attach?.schema, // 字段应用路径
        target: attach?.path, // 目标路径
      },
      openWindowDefine: {
        ...openWindowDefine,
        title: openWindowDefine.title ?? '开窗', // 默认标题
        lang: openWindowDefine.lang ?? {
          title: {
            en_US: 'open window',
            zh_CN: '开窗',
            zh_TW: '開窗',
          },
        },
        allAction: {
          searchInfos: openWindowDefine.allAction?.searchInfos ?? [],
        },
        multipleSelect: openWindowDefine.multipleSelect ?? columnType === 'FORM_OPERATION_EDITOR', // 表单编辑器默认启用多选
        applyToArray: openWindowDefine.applyToArray ?? columnType === 'FORM_OPERATION_EDITOR', // 表单编辑器默认启用数组应用
        supportBatch: openWindowDefine.supportBatch ?? false, // 默认不支持批量操作
        selectedFirstRow: openWindowDefine.selectedFirstRow ?? false, // 默认不选中第一行
        useHasNext: openWindowDefine.useHasNext ?? false, // 默认不使用 hasNext 字段
        enableInputSearch: openWindowDefine.enableInputSearch ?? true, // 默认启用搜索输入框
        buttons: openWindowDefine.buttons ?? [
          {
            id: 'confirm',
            title: '提交',
            lang: {
              title: {
                zh_TW: '提交',
                en_US: 'Submit',
                zh_CN: '提交',
              },
            },
            language: {
              title: {
                zh_TW: '提交',
                en_US: 'Submit',
                zh_CN: '提交',
              },
            },
            actions: [
              {
                backFills: [], // 默认无回填操作
                type: 'UI', // 默认 UI 类型
              },
            ],
          },
        ],
        layout: openWindowDefine.layout ?? combinLayout({ data_name: '', field: [] }), // 默认布局
        type: 'dataConnector',
        dataConnectorId: openWindowDefine?.dataConnectorId,
      },
      condition,
      description: description ?? '',
      lang: lang ?? {
        description: {
          zh_CN: '',
          zh_TW: '',
          en_US: '',
        },
      },
      // navigationConfig: {
      //   enable: navigationConfig.enable ?? false,
      //   url: navigationConfig.url ?? 'base-data-entry',
      //   urlParams: navigationConfig.urlParams ?? {},
      // },
    });

    // 深拷贝并更新工作数据
    let newData = cloneDeep({ ...data, data: newActiveData });
    set(() => ({ workData: newData, cloneActiveData: newData?.data }));

    const newDataConnectorId = openWindowDefine?.dataConnectorId;
    // 高级查询
    if (!isEmpty(openWindowDefine?.dataConnectorId)) {
      const fieldTree = data.fieldTreeMap?.get(newDataConnectorId);
      const { searchInfosTree: newSearchInfosTree, searchInfosType: newSearchInfosType } =
        handleCombinSearchInfosTree(fieldTree ?? []);
      console.log('高级查询数据', newSearchInfosTree, newSearchInfosType);
      set({ searchInfosTree: newSearchInfosTree, searchInfosType: newSearchInfosType });
    }
  },
  updateActiveData: (data: any) => {
    const oldDataConnectorId = get().openWindowDefine()?.dataConnectorId;
    const newDataConnectorId = data?.openWindowDefine?.dataConnectorId;

    let newData = cloneDeep(data);
    set((state) => ({
      workData: {
        ...state.workData,
        data: newData,
      },
    }));

    // 高级查询
    if (!isEmpty(newDataConnectorId) && oldDataConnectorId !== newDataConnectorId) {
      const fieldTree = get().fieldTreeMap()?.get(newDataConnectorId);
      const { searchInfosTree: newSearchInfosTree, searchInfosType: newSearchInfosType } =
        handleCombinSearchInfosTree(fieldTree ?? []);
      console.log('高级查询数据', newSearchInfosTree, newSearchInfosType);
      set({ searchInfosTree: newSearchInfosTree, searchInfosType: newSearchInfosType });
    }
  },
  updateOpenWindowDefine: (data: any) => {
    const oldDataConnectorId = get().openWindowDefine()?.dataConnectorId;
    const newDataConnectorId = data?.dataConnectorId;

    set((state) => ({
      workData: {
        ...state.workData,
        data: {
          ...state.workData.data,
          openWindowDefine: { ...data },
        },
      },
    }));

    // 高级查询
    if (!isEmpty(newDataConnectorId) && oldDataConnectorId !== newDataConnectorId) {
      const fieldTree = get().fieldTreeMap()?.get(newDataConnectorId);
      const { searchInfosTree: newSearchInfosTree, searchInfosType: newSearchInfosType } =
        handleCombinSearchInfosTree(fieldTree ?? []);
      console.log('高级查询数据', newSearchInfosTree, newSearchInfosType);
      set({ searchInfosTree: newSearchInfosTree, searchInfosType: newSearchInfosType });
    }
  },
  updateFields: (data: any) =>
    set((state) => ({
      workData: {
        ...state.workData,
        fields: data,
      },
    })),
  searchInfosTree: [],
  searchInfosType: null,
  // 列配置
  columnDefs: () => get().workData?.data?.openWindowDefine?.layout?.[0]?.columnDefs ?? [],
  updateColumnDefs: (data: any) => {
    set((state) => ({
      workData: {
        ...state.workData,
        data: {
          ...state.workData.data,
          openWindowDefine: {
            ...(state.workData.data?.openWindowDefine ?? {}),
            layout: [
              {
                ...(state.workData.data?.openWindowDefine?.layout?.[0] ?? {}),
                columnDefs: data,
              },
            ],
          },
        },
      },
    }));
  },
}));
