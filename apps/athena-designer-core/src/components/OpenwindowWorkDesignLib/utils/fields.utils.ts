import { isNone } from '@/components/DataSource/tools';
import { t } from 'i18next';
import { cloneDeep, isArray, isEmpty } from 'lodash';
import { v4 as uuid } from 'uuid';

/**
 * AthenaType
 */
export const enum AthenaDataType {
  STRING = 'string',
  NUMERIC = 'numeric',
  DATE = 'date',
  BOOLEAN = 'boolean',
  OBJECT = 'object',
  ARRAY = 'array',
  TIME = 'time',
  DATETIME = 'datetime',
}

// 设计时自用组件: 非标准组件
export enum ATHENA_TYPES_PC_OWN {
  COMMON = 'COMMON',
  INPUT_NUMBER = 'INPUT_NUMBER',
  SWITCH = 'SWITCH',
  CUSTOM = 'CUSTOM',
  TAB_EDIT = 'TAB_EDIT',
  MANAGE_STATUS = 'MANAGE_STATUS',
  BUTTON_GROUP_LIST = 'BUTTON_GROUP_LIST',
  TABLE_GROUP = 'TABLE_GROUP',
  COLLAPSE_EDIT = 'COLLAPSE_EDIT',
  ISV_CUSTOM = 'ISV_CUSTOM', // ISV 自定义组件
}

// 标准组件: 已完成
enum ATHENA_TYPES_PC {
  /* 移动端临时 */
  DW_INPUT_SWITCH = 'DW_INPUT_SWITCH',
  DW_CUSTOMIZE = 'DW_CUSTOMIZE',
  /* 标准组件: 已完成 */
  ACTIVITY_DESCRIPTION = 'ACTIVITY_DESCRIPTION',
  ACTIVITY_TITLE = 'ACTIVITY_TITLE',
  ADDRESS = 'ADDRESS',
  ADD_DOCUMENTID_CONTROL = 'ADD_DOCUMENTID_CONTROL',
  AMOUNT_INPUT = 'AMOUNT_INPUT',
  APPROVAL_DESCRIPTION = 'APPROVAL_DESCRIPTION',
  ATHENA_TABLE = 'ATHENA_TABLE',
  ATH_TAG = 'ATH_TAG', // 标签类控件
  BUSINESS_SUPPLIER = 'BUSINESS_SUPPLIER',
  BUTTON = 'BUTTON',
  CHECKBOX = 'CHECKBOX',
  CHECK_RESULT = 'CHECK_RESULT',
  COLLAPSE = 'COLLAPSE',
  COMBINATION_CHART = 'COMBINATION_CHART',
  CONTACT = 'CONTACT',
  CONTENT_QUERY_BUTTON = 'CONTENT_QUERY_BUTTON',
  CONTENT_TITLE = 'CONTENT_TITLE',
  COUNT_DOWN = 'COUNT_DOWN',
  CURRENT_ACCOUNT = 'CURRENT_ACCOUNT',
  DATEPICKER = 'DATEPICKER',
  DATE_RANGE = 'DATE_RANGE',
  DELIVERY_REPLY_DESCRIPTION = 'DELIVERY_REPLY_DESCRIPTION',
  DELIVERY_REPLY_TITLE = 'DELIVERY_REPLY_TITLE',
  DIFFERENCE_CALCULATION = 'DIFFERENCE_CALCULATION',
  DYNAMIC_GRAPH_VIEWER = 'DYNAMIC_GRAPH_VIEWER',
  EOC_MULTI_SELECT = 'EOC_MULTI_SELECT',
  EOC_SELECT = 'EOC_SELECT',
  EOC_USER_SELECT = 'EOC_USER_SELECT',
  FILE_UPLOAD = 'FILE_UPLOAD',
  FLEXIBLE_BOX = 'FLEXIBLE_BOX', // 行布局
  FORM_LIST = 'FORM_LIST',
  MODAL = 'MODAL',
  FORM_OPERATION_EDITOR = 'FORM_OPERATION_EDITOR',
  FORM_UPLOAD = 'FORM_UPLOAD',
  GRIDSTER = 'GRIDSTER',
  INPUT = 'INPUT',
  LABEL = 'LABEL',
  LAYOUT = 'LAYOUT',
  MEASURE = 'MEASURE',
  NAME_CODE_COMPONENT = 'NAME_CODE_COMPONENT',
  NEW_OLD_COMPONENT = 'NEW_OLD_COMPONENT',
  OPERATION_EDITOR = 'OPERATION_EDITOR',
  PERCENT_INPUT = 'PERCENT_INPUT',
  PLAN_SELECT = 'PLAN_SELECT',
  RADIO_GROUP = 'RADIO_GROUP',
  SELECT = 'SELECT',
  SELECT_MULTIPLE = 'SELECT_MULTIPLE',
  SIGN_OFF_PROGRESS = 'SIGN_OFF_PROGRESS',
  SIGN_OFF_PROGRESS_LINK = 'SIGN_OFF_PROGRESS_LINK',
  PERSON_SELECT = 'PERSON_SELECT',
  TABS = 'TABS',
  TASK_PROGRESS_STATUS = 'TASK_PROGRESS_STATUS',
  TEXTAREA = 'TEXTAREA',
  TEXTAREA_TEXT = 'TEXTAREA_TEXT', // 已实现未注册
  TIMEPICKER = 'TIMEPICKER',
  TIME_RANGE = 'TIME_RANGE',
  TOOLBAR = 'TOOLBAR',
  TREEDATA = 'TREEDATA',
  WORKFLOW_PROGRESS = 'WORKFLOW_PROGRESS',

  COLLAPSE_EDIT = 'COLLAPSE_EDIT',
  ISV_CUSTOM = 'ISV_CUSTOM', // ISV 自定义组件

  DRAWER = 'DRAWER', // demo
  DRAWER_ITEM = 'DRAWER_ITEM',
  DRAWER_EDIT = 'DRAWER_EDIT',
  // 标签模版打印按钮
  BUTTON_PRINT = 'BUTTON_PRINT',
}

// 标准组件: 未完成
enum ATHENA_TYPES_PC_TODO {
  ATTACHMENT = 'ATTACHMENT',
  CARD = 'CARD',
  CASE_SELECT = 'CASE_SELECT',
  CONTENT_SUBTITLE = 'CONTENT_SUBTITLE',
  COllAPSE_AND_EXPAND = 'COllAPSE_AND_EXPAND',
  DATA_FLOW_STATUS = 'DATA_FLOW_STATUS',
  DATA_UNIFORMITY_HISTORY = 'DATA_UNIFORMITY_HISTORY',
  DESCRIPTION = 'DESCRIPTION',
  DRAWER_BUTTON = 'DRAWER_BUTTON',
  DYNAMIC_GRAPH_VIEWER_EDITOR = 'DYNAMIC_GRAPH_VIEWER_EDITOR',
  ECHARTS = 'ECHARTS',
  EOC_SINGLE_SELECT = 'EOC_SINGLE_SELECT',
  FLOW = 'FLOW',
  GROUP_INLINE = 'GROUP_INLINE',
  HOVERLABEL = 'HOVERLABEL',
  IMPORTANCE_CHECK = 'IMPORTANCE_CHECK',
  LINE_TABLE = 'LINE_TABLE',
  LIST = 'LIST',
  MANUAL_CREATE_TASK = 'MANUAL_CREATE_TASK',
  MEASURE_INPUT = 'MEASURE_INPUT',
  MULTI_EOC_SELECT = 'MULTI_EOC_SELECT',
  MULTI_FIELD_LABEL = 'MULTI_FIELD_LABEL',
  NAVIGATE_REPORT = 'NAVIGATE_REPORT',
  NAVIGATE_TASK = 'NAVIGATE_TASK',
  NAVIGATE_TO_TASK = 'NAVIGATE_TO_TASK',
  PANEL = 'PANEL',
  PERSON_IN_CHARGE_SELECT = 'PERSON_IN_CHARGE_SELECT',
  PLACEHOLDER = 'PLACEHOLDER',
  PROGRAMME_PROGRESS = 'PROGRAMME_PROGRESS',
  PROGRESSBAR = 'PROGRESSBAR',
  PROJECT_TABLE = 'PROJECT_TABLE',
  QR_CODE = 'QR_CODE',
  READ_TIMES = 'READ_TIMES',
  SCANNER_GUN_ANALYSIS = 'SCANNER_GUN_ANALYSIS',
  SELECT_CONFIGURABLE = 'SELECT_CONFIGURABLE',
  SLIDER = 'SLIDER',
  SPLIT_ROW = 'SPLIT_ROW',
  STATISTIC = 'STATISTIC',
  TASK_DISTRIBUTION_EXCEPTION_HANDLING = 'TASK_DISTRIBUTION_EXCEPTION_HANDLING',
  TASK_FILE_UPLOAD = 'TASK_FILE_UPLOAD',
  TASK_FORM_UPLOAD = 'TASK_FORM_UPLOAD',
  THUMBNAIL = 'THUMBNAIL',
  UPLOAD_INVOICE = 'UPLOAD_INVOICE',
  WBS_CARDS = 'WBS_CARDS',
}

/**
 * AthenaType
 * 移动端组件类型
 */
// eslint-disable-next-line no-shadow
export enum ATHENA_TYPES_MOBILE {
  DW_GRID_GROUP = 'DW_GRID_GROUP',
  DW_ATTACHMENT = 'DW_ATTACHMENT',
  DW_PHONE_NUMBER = 'DW_PHONE_NUMBER',
  DW_EMAIL = 'DW_EMAIL',
  DW_ID_CARD = 'DW_ID_CARD',
  DW_BANK_CARD = 'DW_BANK_CARD',
  DW_INVOICE_INFO = 'DW_INVOICE_INFO',
  DW_TRAIN_TICKETS = 'DW_TRAIN_TICKETS',
  DW_APP_ENTRANCE = 'DW_APP_ENTRANCE',
  DW_PICTURE = 'DW_PICTURE',
  SCAN = 'SCAN',
  DW_INPUT_SCAN = 'DW_INPUT_SCAN',
  DW_WEBVIEW = 'DW_WEBVIEW',
  MOBILE_LABEL = 'MOBILE_LABEL',
  LOCATION = 'LOCATION',
  DRAWER = 'DRAWER',
  DRAWER_ITEM = 'DRAWER_ITEM',
  DW_DIVIDER = 'DW_DIVIDER',
  DW_EMPLOYEE_LIST = 'DW_EMPLOYEE_LIST',
  DW_GRAPHIC_DISPLAY = 'DW_GRAPHIC_DISPLAY',
  DW_QR_CODE = 'DW_QR_CODE',
  DW_TABLE = 'DW_TABLE',
  DW_TEXT_MULTI = 'DW_TEXT_MULTI',
  DW_HORIZONTAL_PROGRESS = 'DW_HORIZONTAL_PROGRESS',
  DW_PROGRESS_RATE = 'DW_PROGRESS_RATE',
  DW_PERSONNEL_GROUP = 'DW_PERSONNEL_GROUP',
  DW_PAGE_BUTTON = 'DW_PAGE_BUTTON',
  DW_INPUT_SIGN = 'DW_INPUT_SIGN',
  DW_INPUT_SCAN_MULTI = 'DW_INPUT_SCAN_MULTI',
  DW_BUTTON_SINGLE_SELECT = 'DW_BUTTON_SINGLE_SELECT',
  DW_SINGLE_SELECT = 'DW_SINGLE_SELECT',
  DW_INPUT_CASCADE_DISTRICT = 'DW_INPUT_CASCADE_DISTRICT',
  DW_INPUT_CALENDAR = 'DW_INPUT_CALENDAR',
  DW_UNI_SEARCH = 'DW_UNI_SEARCH',
  DW_BUTTON = 'DW_BUTTON',
  DW_BUTTON_JUMP = 'DW_BUTTON_JUMP',
  DW_BUTTON_SAVE = 'DW_BUTTON_SAVE',
  DW_BUTTON_CANCEL = 'DW_BUTTON_CANCEL',
  DW_BUTTON_RESET = 'DW_BUTTON_RESET',
  DW_BUTTON_STOP = 'DW_BUTTON_STOP',
  DW_BUTTON_SUBMIT = 'DW_BUTTON_SUBMIT',
  DW_SWIPER = 'DW_SWIPER',
  DW_NUMBER_LIST = 'DW_NUMBER_LIST',
  DW_ADDER_SUBSTRACTER = 'DW_ADDER_SUBSTRACTER',
  DW_ARTICLE_TITLE = 'DW_ARTICLE_TITLE',
  DW_EMPTY = 'DW_EMPTY',
  DW_WIFI = 'DW_WIFI',
  DW_GEO_LOCATION = 'DW_GEO_LOCATION',
  DW_CARD = 'DW_CARD',
  DW_ZTB_VIEW_LIST = 'DW_ZTB_VIEW_LIST',
  DW_GROUP_CARD = 'DW_GROUP_CARD',
  DW_PAGE_DESC = 'DW_PAGE_DESC',
  DW_PERSONNEL_STATE = 'DW_PERSONNEL_STATE',
  DW_MESSAGE_BAR = 'DW_MESSAGE_BAR',
  DW_PHASE_LIST = 'DW_PHASE_LIST',
  DW_INPUT_OCR = 'DW_INPUT_OCR',
  DW_DRIVE_TIPS = 'DW_DRIVE_TIPS',
  DW_GROUP_SINGLE_SELECT = 'DW_GROUP_SINGLE_SELECT',
  DW_BUTTON_GROUP = 'DW_BUTTON_GROUP',
  DW_CUSTOM_BUTTON = 'DW_CUSTOM_BUTTON',
  DW_SUBMIT_BUTTON = 'DW_SUBMIT_BUTTON',
  DW_BLANK_AREA = 'DW_BLANK_AREA',
  DW_PERSONNEL_STATE_GROUP = 'DW_PERSONNEL_STATE_GROUP',
  DW_CARD_ONE = 'DW_CARD_ONE',
  DW_CARD_LIST = 'DW_CARD_LIST',
  DW_TASK_CRUMBS = 'DW_TASK_CRUMBS',
  DW_TABS_ITEM = 'DW_TABS_ITEM',
  DW_INDEX_NAVBAR = 'DW_INDEX_NAVBAR',
  DW_CHOOSE_TASK_PROGRESS = 'DW_CHOOSE_TASK_PROGRESS',
  DW_CUSTOM_GROUP = 'DW_CUSTOM_GROUP',
  DW_SET_UP_FORM = 'set-up-form',
  DW_AUTO_FILL = 'DW_AUTO_FILL',
  DW_APPOSITION = 'DW_APPOSITION',
  DW_TABS = 'DW_TABS',
  DW_STANDARD_TABS = 'DW_STANDARD_TABS',
  DW_CUSTOM_TAB_CONTROLLER = 'DW_CUSTOM_TAB_CONTROLLER',
  DW_WEBVIEW_PANEL = 'DW_WEBVIEW_PANEL',
  DW_PANEL = 'DW_PANEL',
  DW_TAB_SCROLL_CONTAINER = 'DW_TAB_SCROLL_CONTAINER',
  DW_MULTI_FUNCTION_LIST = 'DW_MULTI_FUNCTION_LIST',
  DW_TASK_LIST = 'DW_TASK_LIST',
  DW_SINGLE_LIST_CONTAINER = 'DW_SINGLE_LIST_CONTAINER',
  DW_HEAD_BODY_CONTAINER = 'DW_HEAD_BODY_CONTAINER',
  DW_SINGLE_OPERATION_EDITOR = 'DW_SINGLE_OPERATION_EDITOR',
  'set-up-page' = 'set-up-page',
  'set-up-drawer' = 'set-up-drawer',
  'single-main-form' = 'single-main-form',
  DW_TASK_PROGRESS_LIST = 'DW_TASK_PROGRESS_LIST',
  DW_WORKFLOW_PROGRESS = 'DW_WORKFLOW_PROGRESS',
  DW_RETRACYABLE = 'DW_RETRACYABLE',
  DW_PROGRESS_BAR = 'DW_PROGRESS_BAR',
  DW_CHART_INDICATOR = 'DW_CHART_INDICATOR',
  DW_CHART_SUBPROGRESS = 'DW_CHART_SUBPROGRESS',
  DW_CHART_PIE = 'DW_CHART_PIE',
  DW_CHART_BAR = 'DW_CHART_BAR',
  DW_CHART_LINE = 'DW_CHART_LINE',
  DW_CHART_DASHBOARD = 'DW_CHART_DASHBOARD',
  DW_CHART_RADAR = 'DW_CHART_RADAR',
  DW_LIST_CONTAINER = 'DW_LIST_CONTAINER',
  DW_LINEAR_LAYOUT = 'DW_LINEAR_LAYOUT',
  DW_AVATAR = 'DW_AVATAR',
}

export const ATHENA_TYPES = {
  ...ATHENA_TYPES_PC_OWN,
  ...ATHENA_TYPES_PC,
  ...ATHENA_TYPES_PC_TODO,
  ...ATHENA_TYPES_MOBILE,
};

/**
 * 格式化树控件字段
 * @param params
 * @returns
 */
export const formatNodes = (params: any): any => {
  let { arr, prefix, layer = 0 } = params;
  arr = arr ?? [];
  if (!isArray(arr)) {
    arr = [arr];
  }
  const results = [];
  arr.forEach((element, index) => {
    const node = {
      value: element.fullPath,
      label:
        element.data_name +
        (element.description?.[t('dj-LANG')]
          ? '(' + element.description?.[t('dj-LANG')] + ')'
          : ''),
    };
    if (element.data_type === 'object') {
      node['children'] = formatNodes({
        arr: element.children,
        prefix: isEmpty(prefix) ? element.data_name : prefix + '.' + element.data_name,
        layer: layer + 1,
      });
    }
    results.push(node);
  });
  return results;
};

/**
 * 组装searchInfosTree
 * @param fieldTree
 * @returns
 */
export const handleCombinSearchInfosTree = (fieldTree: any[]) => {
  let searchInfosTree = [];
  let searchInfosType = new Map([]);
  if (fieldTree.length > 0) {
    searchInfosTree = fieldTree.map((field) => {
      let info = {
        value: field.data_name,
        title: isNone(field.description) ? null : field.description[t('dj-LANG')],
        isLeaf: field.data_type !== 'object',
        origin: {
          ...field,
        },
        children: [],
      };
      searchInfosType.set(field.data_name, field.data_type);
      if (!info.isLeaf) {
        info['children'] = field.children.map((fieldChild) => {
          searchInfosType.set(fieldChild.data_name, fieldChild.data_type);
          return {
            value: fieldChild.data_name,
            title: isNone(fieldChild.description) ? null : fieldChild.description[t('dj-LANG')],
            isLeaf: fieldChild.data_type !== 'object',
            origin: {
              ...fieldChild,
            },
            children: [],
          };
        });
      }
      return info;
    });
  }
  return { searchInfosTree, searchInfosType };
};
