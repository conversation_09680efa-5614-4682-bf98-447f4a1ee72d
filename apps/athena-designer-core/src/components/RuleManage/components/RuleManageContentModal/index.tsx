import React, { useContext, useEffect, useMemo, useState } from 'react';
import {
  useRuleManageContentModalStore,
  useRuleManageRuleListStore,
  useRuleManageStore,
} from '../../store';
import i18n, { t } from 'i18next';
import {
  Button,
  Checkbox,
  Dropdown,
  Flex,
  Form,
  Input,
  InputNumber,
  Modal,
  Select,
  Space,
  Tooltip,
  message,
} from 'antd';
import './index.less';
import Icon from '@/components/Icon';
import SelectFieldModal from './SelectFieldModal';
import { AthTreeDataNode } from './SelectFieldModal/type';
import AppLangInput from '@/components/AppLangInput';
import { MinusCircleOutlined, PlusCircleOutlined, RightSquareOutlined } from '@ant-design/icons';
import {
  validateTypes,
  partternList,
  contentTiggerPoint,
  contentTiggerTypes,
  contentTiggerApiPrefixType,
  contentTiggerApiMethodType,
  contentRelationsOperationsType,
} from '../../config/ruleData.config';
import { removeNullValues, checkKeyPathInRuleData } from '../../utils/tools';
import { saveRule } from '../../api/request';
import { cloneDeep } from 'lodash';
import { globalEventEmitter } from '@/common/hooks';
import AiAssistant from '@/components/AiAssistant';
import { v4 as uuidv4 } from 'uuid';
import AuthWrapper from '@/components/AuthWrapper';
import { ResourceType } from '@/common/utils/auth-util';
import { RuleManageEventType } from '../../config/ruleManage.type';
import { MonacoEditor } from '@/components/MonacoEditor';
import { SupportedEditorType } from '@/components/MonacoEditor/enum';
import { OfflineRuleListChange } from '../../context';

interface FormLableProps {
  title: string;
  tips?: string;
}
const FormLable: React.FC<FormLableProps> = (props: FormLableProps) => {
  return (
    <span>
      <span>{props.title} </span>
      {props.tips && (
        <Tooltip title={props.tips}>
          <Icon type="iconexplain"></Icon>
        </Tooltip>
      )}
    </span>
  );
};

interface RuleManageContentModalProps {}
const RuleManageContentModal: React.FC<RuleManageContentModalProps> = () => {
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  // 是否打开选择字段弹窗
  const [isOpenSelectFieldModal, setIsOpenSelectFieldModal] = useState(false);
  // 是否打开参数选择下拉
  const [isOpenParameterDropdown, setIsOpenParameterDropdown] = useState(false);
  // 选择字段作用的form name path
  const [selectFieldPath, setSelectFieldPath] = useState([]);
  // 是否展示触发动作id
  const [showTriggerActionId, setShowTriggerActionId] = useState(false);
  // 是否是异步触发
  const [isTriggerTypeAsync, setIsTriggerTypeAsync] = useState(false);
  // 是否需要自由写脚本
  const [isTriggerNeedCustomEvaluation, setIsTriggerNeedCustomEvaluation] = useState(false);
  // 是否是批量调用
  const [isBatchRequest, setIsBatchRequest] = useState(false);
  // 参数
  const [parameter, setParameter] = useState('');
  // 是否打开脚本编辑器弹窗
  const [isOpenMonacoEditorModal, setIsOpenMonacoEditorModal] = useState(false);
  // 脚本编辑器弹窗的初值
  const [monacoEditorModalValue, setMonacoEditorModalValue] = useState(null);
  // 脚本编辑器作用的form name path
  const [monacoEditorPath, setMonacoEditorPath] = useState([]);

  const { isOpen, setIsOpen, type, ruleData, setRuleData } = useRuleManageContentModalStore(
    (state) => ({
      isOpen: state.isOpen,
      setIsOpen: state.setIsOpen,
      type: state.type,
      ruleData: state.ruleData,
      setRuleData: state.setRuleData,
    })
  );

  const { ruleList, setRuleList } = useRuleManageRuleListStore((state) => ({
    ruleList: state.ruleList,
    setRuleList: state.setRuleList,
  }));

  const { ruleManageOriginData, contextDataSourceName, setContextDataSourceName } =
    useRuleManageStore((state) => ({
      ruleManageOriginData: state.ruleManageOriginData,
      contextDataSourceName: state.contextDataSourceName,
      setContextDataSourceName: state.setContextDataSourceName,
    }));

  const dataSourceName = useMemo(() => {
    return contextDataSourceName ?? ruleManageOriginData?.defaultDataSourceName ?? '';
  }, [contextDataSourceName, ruleManageOriginData.defaultDataSourceName]);

  const offlineRuleListChange = useContext(OfflineRuleListChange);

  useEffect(() => {
    form.resetFields();
    const { content, ...rest } = ruleData ?? {};
    /**
     * 存在默认值linkageSchemas为空字符串的情况，造成保存报错
     */
    const { linkageSchemas, ...cRest } = content ?? {};
    form.setFieldsValue({
      ...rest,
      content: {
        ...cRest,
        linkageSchemas: linkageSchemas ? linkageSchemas : [],
      },
    });
    handleOnFieldsChange();
  }, [ruleData]);

  const handleOnFieldsChange = () => {
    setShowTriggerActionId(form.getFieldValue(['content', 'trigger', 'point']) === 'inSubmit');
    setIsTriggerTypeAsync(form.getFieldValue(['content', 'trigger', 'type']) === 'async');
    setIsTriggerNeedCustomEvaluation(
      form.getFieldValue(['content', 'trigger', 'needCustomEvaluation'])
    );
    setIsBatchRequest(form.getFieldValue(['content', 'batchRequest']));
    setParameter(form.getFieldValue(['content', 'parameter']));
  };

  // 处理名称变更
  const handleNameChange = (value: DV.LangConfig) => {
    form.setFieldValue(['content', 'name'], value[i18n.language]);
    form.setFieldValue(['content', 'lang', 'name'], value);
  };

  // 处理errorMessage
  const handleErrorMessageChange = (value: DV.LangConfig) => {
    form.setFieldValue(['content', 'errorMessage'], value[i18n.language]);
    form.setFieldValue(['content', 'lang', 'errorMessage'], value);
  };

  // 打开选择字段弹窗
  const handleOpenSelectFieldModal = (fieldPath: string[]) => {
    setSelectFieldPath(fieldPath);
    setIsOpenSelectFieldModal(true);
  };

  // 选择字段弹窗确认
  const handleSelectFieldModalSubmit = (nodes: AthTreeDataNode[]) => {
    setIsOpenSelectFieldModal(false);

    // 特殊逻辑，如果是path是作用字段会同时赋值作用路径
    if (selectFieldPath.join('-') === ['content', 'schema'].join('-')) {
      form.setFieldValue(selectFieldPath, nodes[0]?.data_name);
      form.setFieldValue(['content', 'path'], nodes[0]?.path);
    }

    form.setFieldValue(selectFieldPath, nodes[0]?.data_name);
  };

  // 选择字段弹窗取消
  const handleSelectFieldModalCancel = () => {
    setIsOpenSelectFieldModal(false);
  };

  // 打开脚本编辑器弹窗
  const handleOpenMonacoEditorModal = (fieldPath: any[]) => {
    setMonacoEditorPath(fieldPath);
    setMonacoEditorModalValue(form.getFieldValue(fieldPath) ?? '');
    setIsOpenMonacoEditorModal(true);
  };

  // 脚本编辑器弹窗确认
  const handleMonacoEditorModalSubmit = (data: any) => {
    setIsOpenMonacoEditorModal(false);
    form.setFieldValue(monacoEditorPath, data);
  };

  // 脚本编辑器弹窗取消
  const handleMonacoEditorCancel = () => {
    setMonacoEditorModalValue(null);
    setIsOpenMonacoEditorModal(false);
  };

  const handleChangeParameter = (key) => {
    form.setFieldValue(['content', 'parameter'], key);
    handleOnFieldsChange();
    setIsOpenParameterDropdown(false);
  };

  // 格式化规则数据，迁移之前的逻辑
  const formatFormValue = (formValue) => {
    const returnValue = removeNullValues(cloneDeep(formValue));

    if (typeof returnValue.content?.parameter === 'number') {
      returnValue.content.parameter = returnValue.content.parameter.toString();
    }
    if ('' === returnValue.content.condition) {
      Reflect.deleteProperty(returnValue.content, 'condition');
    }

    // 规则配置支持异步批量处理配置
    if (returnValue.content?.trigger?.type === 'async') {
      // 去除apiUrl首尾空格
      returnValue.content.trigger['apiUrl'] = returnValue.content.trigger.apiUrl.replace(
        /\s+/g,
        ''
      );
    }
    return returnValue;
  };

  /**
   * currentRuleData当前节点的规则
   * @param content
   * @returns
   */
  const ruleIsExist = (content: any): boolean => {
    const onlyArr = [
      'required',
      'min',
      'max',
      'minLength',
      'maxLength',
      'repeat',
      'disabled',
      'hidden',
      'value',
      'defaultValue',
      'rowDefaultValue',
    ];

    if (!onlyArr.includes(content.key)) return false;

    return !!ruleList.find((rule) => {
      return (
        rule.content.path === content.path &&
        rule.content.schema === content.schema &&
        rule.content.key === content.key
      );
    });
  };

  const handleModalOk = async () => {
    try {
      await form.validateFields();
      const formValue = await form.getFieldsValue();
      let returnRuleData = formatFormValue(formValue);

      if (type === 'create') {
        if (ruleIsExist(returnRuleData.content)) {
          message.info(t('dj-该类型的规则已存在'));
          return;
        }
      } else {
        returnRuleData = { ...ruleData, ...returnRuleData };
      }

      const params = {
        application: ruleManageOriginData?.applicationCode,
        domainId: ruleManageOriginData?.code,
        domain: ruleManageOriginData?.domainType,
        ...returnRuleData,
      };
      if (ruleManageOriginData.isOffline) {
        setIsOpen(false);
        if (type === 'create') {
          // 离线规则新增会使用前端生成的uuid，非离线规则后端会生成uuid
          params['key'] = uuidv4();
          message.success(t('dj-保存成功'));
          // setRuleList([...ruleList, params]);
          offlineRuleListChange([...ruleList, params]);
          return;
        }
        const findIndex = ruleList.findIndex((rule) => {
          return rule.key === params.key;
        });

        if (findIndex >= 0) {
          message.success(t('dj-保存成功'));
          const newRuleList = [...ruleList];
          newRuleList.splice(findIndex, 1, params);
          // setRuleList(newRuleList);
          offlineRuleListChange(newRuleList);
          return;
        }

        message.error(t('dj-保存失败'));
        return;
      }

      setIsLoading(true);
      const { data: resData } = await saveRule(params);
      if (resData.code === 0) {
        setIsOpen(false);
        message.success(t('dj-保存成功'));
        globalEventEmitter.emit(RuleManageEventType.ruleManageReloadList);
      } else {
        message.error(resData.msg);
      }
    } catch (error) {
      const messageText =
        error?.response?.data?.msg ??
        error?.message ??
        error?.values?.content?.errorMessage ??
        t('dj-保存失败');
      message.error(messageText);
    }
    setIsLoading(false);
  };

  const handlecCancel = () => {
    setIsOpen(false);
    setContextDataSourceName(null);
  };

  const handleReset = () => {
    setRuleData(null);
    setContextDataSourceName(null);
  };

  return (
    <>
      <Modal
        open={isOpen}
        title={type === 'create' ? t('dj-新增规则') : t('dj-编辑规则')}
        getContainer={false}
        footer={null}
        onCancel={handlecCancel}
        destroyOnClose={true}
        maskClosable={false}
        width={1100}
        className="rule-manage-content-modal"
        afterClose={handleReset}
      >
        <div className="rule-manage-content-modal-content">
          <Form
            labelCol={{ span: 7 }}
            wrapperCol={{ span: 17 }}
            form={form}
            autoComplete="off"
            initialValues={{}}
            onValuesChange={handleOnFieldsChange}
          >
            <div className="content-title">{t('dj-编辑规则')}</div>
            <div className="content-fields">
              <Form.Item
                name={['content', 'key']}
                label={<FormLable title={t('dj-规则类型')} tips="content.key"></FormLable>}
                colon={false}
              >
                <Input disabled={true} />
              </Form.Item>
              <Form.Item
                name={['content', 'name']}
                label={<FormLable title={t('dj-名称')} tips="content.name"></FormLable>}
                hidden={true}
              >
                <Input />
              </Form.Item>

              <Form.Item
                label={<FormLable title={t('dj-名称')} tips="content.name"></FormLable>}
                name={['content', 'lang', 'name']}
              >
                <AppLangInput
                  className="form-item-input"
                  required
                  size="small"
                  title={t('dj-名称')}
                  onChange={(value) => handleNameChange(value)}
                  placeholder={t('dj-名称')}
                />
              </Form.Item>

              <Form.Item
                name="remark"
                label={<FormLable title={t('dj-备注')} tips="remark"></FormLable>}
                colon={false}
              >
                <Input />
              </Form.Item>
              <Form.Item
                name={['content', 'path']}
                label={<FormLable title={t('dj-作用路径')} tips="content.path"></FormLable>}
                colon={false}
              >
                <Input disabled={type !== 'create'} />
              </Form.Item>
              <Form.Item
                name={['content', 'schema']}
                label={<FormLable title={t('dj-作用字段')} tips="content.schema"></FormLable>}
                colon={false}
              >
                <Input
                  className="content-fields-item-field-input"
                  disabled={type !== 'create'}
                  suffix={
                    <RightSquareOutlined
                      onClick={() => {
                        if (type !== 'create') return;
                        handleOpenSelectFieldModal(['content', 'schema']);
                      }}
                    />
                  }
                />
              </Form.Item>

              {checkKeyPathInRuleData(ruleData, ['content', 'linkageSchemas']) && (
                <Form.Item
                  name={['content', 'linkageSchemas']}
                  label={
                    <FormLable title={t('dj-联动字段')} tips="content.linkageSchemas"></FormLable>
                  }
                  colon={false}
                >
                  <Select
                    className="content-fields-item-field-select"
                    mode="tags"
                    size="small"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              )}

              <Form.Item
                name={['content', 'targetSchema']}
                label={<FormLable title={t('dj-触发字段')} tips="content.targetSchema"></FormLable>}
                colon={false}
              >
                <Input
                  className="content-fields-item-field-input"
                  suffix={
                    <RightSquareOutlined
                      onClick={() => {
                        handleOpenSelectFieldModal(['content', 'targetSchema']);
                      }}
                    />
                  }
                />
              </Form.Item>

              <Form.Item
                name={['content', 'validatorType']}
                label={
                  <FormLable title={t('dj-校验类型')} tips="content.validatorType"></FormLable>
                }
                colon={false}
              >
                <Select
                  className="content-fields-item-field-select"
                  options={validateTypes}
                  size="small"
                  fieldNames={{ label: 'desc', value: 'key' }}
                  style={{ width: '100%' }}
                  optionRender={(option) => <Space>{t(option.data.desc)}</Space>}
                  labelRender={(props) => {
                    return t(props.label as string);
                  }}
                />
              </Form.Item>

              <Form.Item
                name={['content', 'condition']}
                label={<FormLable title={t('dj-条件')} tips="content.condition"></FormLable>}
                colon={false}
              >
                <Input />
              </Form.Item>

              {checkKeyPathInRuleData(ruleData, ['content', 'valueScript']) && (
                <Form.Item
                  name={['content', 'valueScript']}
                  label={
                    <FormLable title={t('dj-赋值脚本')} tips="content.valueScript"></FormLable>
                  }
                  colon={false}
                >
                  <Input
                    onDoubleClick={() => {
                      handleOpenMonacoEditorModal(['content', 'valueScript']);
                    }}
                  />
                </Form.Item>
              )}

              <Form.Item
                name={['content', 'asyncCacheScope']}
                label={
                  <FormLable
                    title={t('dj-异步缓存作用域')}
                    tips="content.asyncCacheScope"
                  ></FormLable>
                }
                colon={false}
              >
                <Input />
              </Form.Item>

              <Form.Item
                name={['content', 'scope']}
                label={<FormLable title={t('dj-范围')} tips="content.scope"></FormLable>}
                colon={false}
              >
                <Input />
              </Form.Item>

              <Form.Item
                name={['content', 'errorMessage']}
                label={<FormLable title={t('dj-错误信息')} tips="content.errorMessage"></FormLable>}
                hidden={true}
              >
                <Input />
              </Form.Item>

              <Form.Item
                label={<FormLable title={t('dj-错误信息')} tips="content.errorMessage"></FormLable>}
                name={['content', 'lang', 'errorMessage']}
              >
                <AppLangInput
                  className="form-item-input"
                  required
                  size="small"
                  title={t('dj-错误信息')}
                  onChange={(value) => handleErrorMessageChange(value)}
                  placeholder={t('dj-错误信息')}
                />
              </Form.Item>

              {checkKeyPathInRuleData(ruleData, ['content', 'parameter']) &&
                ruleData?.content.key !== 'pattern' && (
                  <Form.Item
                    name={['content', 'parameter']}
                    label={<FormLable title={t('dj-参数')} tips="content.parameter"></FormLable>}
                    colon={false}
                  >
                    <InputNumber
                      size="small"
                      className="content-fields-item-field-input-number"
                      disabled={false}
                    />
                  </Form.Item>
                )}

              {checkKeyPathInRuleData(ruleData, ['content', 'parameter']) &&
                ruleData?.content.key === 'pattern' && (
                  <div className="content-fields-item-field-parameter-content">
                    <Form.Item
                      name={['content', 'parameter']}
                      label={<FormLable title={t('dj-参数')} tips="content.parameter"></FormLable>}
                      colon={false}
                    >
                      <Dropdown
                        trigger={['click']}
                        open={isOpenParameterDropdown}
                        onOpenChange={setIsOpenParameterDropdown}
                        getPopupContainer={() =>
                          document.querySelector('.parameter-content-dropdown-parent')
                        }
                        dropdownRender={() => (
                          <div className="content-fields-item-field-dropdown">
                            {partternList.map((item) => {
                              return (
                                <div
                                  key={item.key}
                                  className="content-fields-item-field-dropdown-item"
                                  onClick={() => {
                                    handleChangeParameter(item.key);
                                  }}
                                >
                                  {`${t(item.name)}:${item.key}`}
                                </div>
                              );
                            })}
                          </div>
                        )}
                      >
                        <Input
                          className="content-fields-item-field-parameter-content-input"
                          value={parameter}
                          onChange={(e) => {
                            handleChangeParameter(e.target.value);
                          }}
                        />
                      </Dropdown>
                    </Form.Item>
                    <AiAssistant
                      type="askingRegular"
                      style={{ marginLeft: '5px' }}
                      backfill={(data) => {
                        console.log(data);
                        handleChangeParameter(data);
                      }}
                    />
                    {/* 在micro app环境下，Dropdown的位置可能 错误，在此进行兼容 */}
                    <div className="parameter-content-dropdown-parent"></div>
                  </div>
                )}

              {checkKeyPathInRuleData(ruleData, ['content', 'crossFieldOrNot']) && (
                <Form.Item
                  name={['content', 'crossFieldOrNot']}
                  label={
                    <FormLable title={t('dj-是否跨行')} tips="content.crossFieldOrNot"></FormLable>
                  }
                  valuePropName="checked"
                  colon={false}
                >
                  <Checkbox></Checkbox>
                </Form.Item>
              )}

              {checkKeyPathInRuleData(ruleData, ['content', 'relationIsLinkage']) && (
                <Form.Item
                  name={['content', 'relationIsLinkage']}
                  valuePropName="checked"
                  label={
                    <FormLable
                      title={t('dj-是否联动触发')}
                      tips="content.relationIsLinkage"
                    ></FormLable>
                  }
                  colon={false}
                >
                  <Checkbox></Checkbox>
                </Form.Item>
              )}
            </div>

            <div className="content-title">{t('dj-触发机制')}</div>
            <div className="content-fields">
              <Form.Item
                name={['content', 'trigger', 'point']}
                label={
                  <FormLable title={t('rule-触发时机')} tips="content.trigger.point"></FormLable>
                }
                colon={false}
              >
                <Select
                  className="content-fields-item-field-select"
                  options={contentTiggerPoint}
                  size="small"
                  fieldNames={{ label: 'key', value: 'key' }}
                  style={{ width: '100%' }}
                />
              </Form.Item>

              <Form.Item
                name={['content', 'trigger', 'type']}
                label={
                  <FormLable title={t('rule-触发类型')} tips="content.trigger.type"></FormLable>
                }
                colon={false}
              >
                <Select
                  className="content-fields-item-field-select"
                  options={contentTiggerTypes}
                  size="small"
                  fieldNames={{ label: 'key', value: 'key' }}
                  style={{ width: '100%' }}
                />
              </Form.Item>

              {checkKeyPathInRuleData(ruleData, ['content', 'trigger', 'condition']) && (
                <Form.Item
                  name={['content', 'trigger', 'condition']}
                  label={
                    <FormLable
                      title={t('dj-触发条件')}
                      tips="content.trigger.condition"
                    ></FormLable>
                  }
                  colon={false}
                >
                  <Input disabled={false} />
                </Form.Item>
              )}

              {showTriggerActionId && (
                <Form.Item
                  name={['content', 'trigger', 'actionId']}
                  label={
                    <FormLable
                      title={t('dj-提交actionId')}
                      tips="content.trigger.actionId"
                    ></FormLable>
                  }
                  colon={false}
                >
                  <Input disabled={false} />
                </Form.Item>
              )}

              {isTriggerTypeAsync && (
                <Form.Item
                  name={['content', 'trigger', 'apiUrl']}
                  rules={[
                    {
                      required: true,
                      message: t('dj-必填'),
                    },
                  ]}
                  label={
                    <FormLable title={t('dj-异步API')} tips="content.trigger.apiUrl"></FormLable>
                  }
                  colon={false}
                >
                  <Input />
                </Form.Item>
              )}

              {isTriggerTypeAsync && (
                <Form.Item
                  name={['content', 'trigger', 'apiPrefixType']}
                  rules={[
                    {
                      required: true,
                      message: t('dj-必填'),
                    },
                  ]}
                  label={
                    <FormLable title={'domain'} tips="content.trigger.apiPrefixType"></FormLable>
                  }
                  colon={false}
                >
                  <Select
                    className="content-fields-item-field-select"
                    options={contentTiggerApiPrefixType}
                    size="small"
                    fieldNames={{ label: 'key', value: 'key' }}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              )}

              {isTriggerTypeAsync && (
                <Form.Item
                  name={['content', 'trigger', 'apiMethodType']}
                  label={
                    <FormLable
                      title={t('dj-请求方式')}
                      tips="content.trigger.apiMethodType"
                    ></FormLable>
                  }
                  colon={false}
                >
                  <Select
                    className="content-fields-item-field-select"
                    options={contentTiggerApiMethodType}
                    size="small"
                    fieldNames={{ label: 'key', value: 'key' }}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              )}

              {isTriggerTypeAsync && (
                <Form.Item
                  name={['content', 'trigger', 'needValidateParameter']}
                  label={
                    <FormLable
                      title={t('dj-需要异步校验')}
                      tips="content.trigger.needValidateParameter"
                    ></FormLable>
                  }
                  valuePropName="checked"
                  colon={false}
                >
                  <Checkbox></Checkbox>
                </Form.Item>
              )}

              {isTriggerTypeAsync && (
                <Form.Item
                  name={['content', 'trigger', 'needCustomEvaluation']}
                  label={
                    <FormLable
                      title={t('dj-需要自由写脚本')}
                      tips="content.trigger.needCustomEvaluation"
                    ></FormLable>
                  }
                  valuePropName="checked"
                  colon={false}
                >
                  <Checkbox></Checkbox>
                </Form.Item>
              )}

              {isTriggerTypeAsync && isTriggerNeedCustomEvaluation && (
                <Form.Item
                  name={['content', 'trigger', 'evaluationScript']}
                  label={
                    <FormLable
                      title={t('dj-异步赋值脚本')}
                      tips="content.trigger.evaluationScript"
                    ></FormLable>
                  }
                  colon={false}
                >
                  <Input
                    onDoubleClick={() => {
                      handleOpenMonacoEditorModal(['content', 'trigger', 'evaluationScript']);
                    }}
                  />
                </Form.Item>
              )}

              {checkKeyPathInRuleData(ruleData, ['content', 'trigger', 'parameterScript']) && (
                <Form.Item
                  name={['content', 'trigger', 'parameterScript']}
                  label={
                    <FormLable
                      title={t('dj-参数脚本')}
                      tips="content.trigger.parameterScript"
                    ></FormLable>
                  }
                  colon={false}
                >
                  <Input
                    disabled={false}
                    onDoubleClick={() => {
                      handleOpenMonacoEditorModal(['content', 'trigger', 'parameterScript']);
                    }}
                  />
                </Form.Item>
              )}

              {isTriggerTypeAsync && (
                <Form.Item
                  name={['content', 'batchRequest']}
                  label={
                    <FormLable title={t('dj-批量调用')} tips="content.batchRequest"></FormLable>
                  }
                  valuePropName="checked"
                  colon={false}
                >
                  <Checkbox></Checkbox>
                </Form.Item>
              )}

              {isBatchRequest && (
                <Form.Item
                  name={['content', 'batchRequest', 'requestQueueSize']}
                  label={
                    <FormLable
                      title={t('dj-最大请求数量')}
                      tips="content.batchRequest.requestQueueSize"
                    ></FormLable>
                  }
                  colon={false}
                >
                  <InputNumber
                    size="small"
                    className="content-fields-item-field-input-number"
                    disabled={false}
                  />
                </Form.Item>
              )}

              {isBatchRequest && (
                <Form.Item
                  name={['content', 'batchRequest', 'identitySchema']}
                  label={
                    <FormLable
                      title={t('dj-校验字段')}
                      tips="content.batchRequest.identitySchema"
                    ></FormLable>
                  }
                  colon={false}
                >
                  <Input
                    className="content-fields-item-field-input"
                    suffix={
                      <RightSquareOutlined
                        onClick={() => {
                          setSelectFieldPath(['content', 'batchRequest', 'identitySchema']);
                          setIsOpenSelectFieldModal(true);
                        }}
                      />
                    }
                  />
                </Form.Item>
              )}

              {/* <!-- 联动规则 --> */}
              {checkKeyPathInRuleData(ruleData, ['content', 'relations']) && (
                <Form.List name={['content', 'relations']}>
                  {(fields, { add, remove }) => (
                    <div className="relations-form">
                      <div className="content-title">
                        {t('dj-联动规则')}
                        <PlusCircleOutlined onClick={() => add()} className="relations-add" />
                      </div>
                      {fields.map(({ key, name: relationName, ...restField }) => (
                        <div key={key} className="relations-form-item">
                          <Form.Item
                            className="relations-form-item-path"
                            name={[relationName, 'path']}
                            label={
                              <FormLable
                                title={t('dj-联动路径')}
                                tips="content.relations.path"
                              ></FormLable>
                            }
                            colon={false}
                          >
                            <Input disabled={false} />
                          </Form.Item>
                          <MinusCircleOutlined
                            className="relations-form-item-remove"
                            onClick={() => remove(relationName)}
                          />
                          <Form.List name={[relationName, 'operations']}>
                            {(fields, { add: addOperation, remove: removeOperation }) => (
                              <div className="relations-form-item-operations">
                                <div className="content-title">
                                  {t('dj-联动操作')}
                                  <PlusCircleOutlined
                                    onClick={() => addOperation()}
                                    className="relations-add"
                                  />
                                </div>
                                {fields.map(({ key, name: operationName, ...restField }) => (
                                  <div key={key} className="relations-form-item-operations-item">
                                    <Form.Item
                                      name={[operationName, 'type']}
                                      label={
                                        <FormLable
                                          title={t('dj-联动类型')}
                                          tips="content.relations.operations.type"
                                        ></FormLable>
                                      }
                                      colon={false}
                                    >
                                      <Select
                                        className="content-fields-item-field-select"
                                        options={contentRelationsOperationsType}
                                        size="small"
                                        fieldNames={{ label: 'key', value: 'key' }}
                                        style={{ width: '100%' }}
                                      />
                                    </Form.Item>
                                    <Form.Item
                                      name={[operationName, 'name']}
                                      label={
                                        <FormLable
                                          title={t('dj-名称')}
                                          tips="content.relations.operations.name"
                                        ></FormLable>
                                      }
                                      colon={false}
                                    >
                                      <Input disabled={false} />
                                    </Form.Item>
                                    <Form.Item
                                      name={[operationName, 'script']}
                                      label={
                                        <FormLable
                                          title={t('dj-脚本')}
                                          tips="content.relations.operations.script"
                                        ></FormLable>
                                      }
                                      colon={false}
                                    >
                                      <Input
                                        disabled={false}
                                        onDoubleClick={() => {
                                          handleOpenMonacoEditorModal([
                                            'content',
                                            'relations',
                                            relationName,
                                            'operations',
                                            operationName,
                                            'script',
                                          ]);
                                        }}
                                      />
                                    </Form.Item>
                                    <MinusCircleOutlined
                                      className="relations-form-item-operations-item-remove"
                                      onClick={() => removeOperation(operationName)}
                                    />
                                  </div>
                                ))}
                              </div>
                            )}
                          </Form.List>
                        </div>
                      ))}
                    </div>
                  )}
                </Form.List>
              )}
            </div>
          </Form>
        </div>
        <Flex className="rule-manage-content-modal-footer" gap="large" wrap="wrap" justify="center">
          <Button onClick={handlecCancel}>{t('dj-取消')}</Button>
          {
            !ruleManageOriginData?.hideAuth ? (
              <AuthWrapper
                authParams={{
                  action: 'update',
                }}
              >
                <Button type="primary" onClick={handleModalOk} loading={isLoading}>
                  {t('dj-确定')}
                </Button>
              </AuthWrapper>
            ) : <Button type="primary" onClick={handleModalOk} loading={isLoading}>
              {t('dj-确定')}
            </Button>
          }
        </Flex>
      </Modal>
      <SelectFieldModal
        fieldTreeMap={ruleManageOriginData.fieldTreeMap}
        dataSourceName={dataSourceName}
        isOpen={isOpenSelectFieldModal}
        onCancel={handleSelectFieldModalCancel}
        onSubmit={handleSelectFieldModalSubmit}
      ></SelectFieldModal>
      <MonacoEditor
        value={monacoEditorModalValue}
        visible={isOpenMonacoEditorModal}
        type={SupportedEditorType.JAVASCRIPT}
        onOk={handleMonacoEditorModalSubmit}
        onCancel={handleMonacoEditorCancel}
      />
    </>
  );
};

export default RuleManageContentModal;
