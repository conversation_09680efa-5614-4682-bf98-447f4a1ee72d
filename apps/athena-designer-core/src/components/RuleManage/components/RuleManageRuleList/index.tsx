import React, { useEffect, useMemo, useState } from 'react';
import { debounce, isEmpty, isEqual } from 'lodash';
import { App, Empty, Input, Popover, Spin } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { t } from 'i18next';
import RuleManageAddSelect from './RuleManageAddSelect';
import Icon from '@/components/Icon';
import './index.less';
import { HidePopover } from './context';
import {
  useRuleManageCollectionModalStore,
  useRuleManageContentModalStore,
  useRuleManageRuleListStore,
  useRuleManageStore,
} from '../../store';
import { useGlobalEventEmitter } from '@/common/hooks/index';
import { useQueryGetRuleList, QueryKeyType } from '../../api/query';
import { useQueryClient } from '@tanstack/react-query';
import RuleManageRuleListItem from './RuleManageRuleListItem';
import AuthWrapper from '@/components/AuthWrapper';
import { RuleManageEventType } from '../../config/ruleManage.type';

const RuleManageRuleList: React.FC = () => {
  const { ruleManageOriginData } = useRuleManageStore((state) => ({
    ruleManageOriginData: state.ruleManageOriginData,
  }));

  // 隐藏快捷键、权限项（例如2.0设计器中全部隐藏）
  const { hideShortcut, hideAuth } = ruleManageOriginData;

  // 规则列表store
  const {
    ruleList = [],
    setRuleList,
    selectedRuleKey,
    setSelectedRuleKey,
    externalAddSelectInfo,
  } = useRuleManageRuleListStore((state) => ({
    ruleList: state.ruleList,
    setRuleList: state.setRuleList,
    selectedRuleKey: state.selectedRuleKey,
    setSelectedRuleKey: state.setSelectedRuleKey,
    externalAddSelectInfo: state.externalAddSelectInfo,
  }));

  // 规则收藏弹窗store
  const { setIsOpen: setCollectionModalIsOpen, setFavoriteRule } =
    useRuleManageCollectionModalStore((state) => ({
      isOpen: state.isOpen,
      setIsOpen: state.setIsOpen,
      setFavoriteRule: state.setFavoriteRule,
    }));

  // 规则面板弹窗store
  const { setIsOpen, setType, setRuleData } = useRuleManageContentModalStore((state) => ({
    setIsOpen: state.setIsOpen,
    setType: state.setType,
    setRuleData: state.setRuleData,
  }));

  const [searchValue, setSearchValue] = useState('');
  const [pasteRule, setPasteRule] = useState(null);
  const [openAddSelect, setOpenAddSelect] = useState(false);

  const { message } = App.useApp();

  //  展示的规则列表
  const showRuleList = useMemo(() => {
    return ruleList.filter((item: any) => {
      return (
        item.key?.includes(searchValue) ||
        item.code?.includes(searchValue) ||
        item.name?.includes(searchValue) ||
        item?.content?.name?.includes(searchValue)
      );
    });
  }, [searchValue, ruleList]);

  const isUseQueryGetRuleListEnable = useMemo(() => {
    const { isOffline, applicationCode, code, domainType } = ruleManageOriginData;
    return !isOffline && !!applicationCode && !!code && !!domainType;
  }, [ruleManageOriginData]);

  const queryClient = useQueryClient();
  const param = {
    application: ruleManageOriginData?.applicationCode,
    domainId: ruleManageOriginData?.code,
    domain: ruleManageOriginData?.domainType,
  };
  const {
    status,
    data: ruleListRes,
    refetch,
  } = useQueryGetRuleList(param, isUseQueryGetRuleListEnable);

  useEffect(() => {
    updataPasteRule();
    const handleStorageChange = (event) => {
      if (event.key === 'taskSet@ruleData') {
        updataPasteRule();
      }
    };
    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  useEffect(() => {
    if (!ruleManageOriginData?.isOffline) {
      const ruleListData = (ruleListRes?.data ?? []).filter((item) => !isEmpty(item?.content));
      setRuleList(ruleListData);
      setSelectedRuleKey(ruleListData[0]?.key ?? null);
    }
  }, [ruleListRes, ruleManageOriginData]);

  const updataPasteRule = () => {
    const ruleData = localStorage.getItem('taskSet@ruleData');
    const parseRule = ruleData ? JSON.parse(ruleData) : null;
    setPasteRule(parseRule);
  };

  // 刷新规则列表
  const ruleManageReloadList = () => {
    if (ruleManageOriginData.isOffline) return;
    // 如果是不是离线规则,才去刷新
    queryClient.removeQueries({ queryKey: [QueryKeyType.queryPageViewList] });
    refetch();
  };

  const searchValueChange = (e: any) => {
    setSearchValue(e.target?.value);
  };

  const handleOpenAddSelectChange = (value: boolean) => {
    setOpenAddSelect(value);
  };

  /**
   * 跨浏览器标签页复制(之前的逻辑)
   */
  const handleCopy2Storage = () => {
    const selectedRule = ruleList.find((item: any) => item.key === selectedRuleKey);
    if (!selectedRule) {
      message.error(t('dj-请选择一个规则再复制'));
      return;
    }
    message.success(t('dj-复制成功，可以粘贴了'));
    localStorage.setItem('taskSet@ruleData', JSON.stringify(selectedRule));
    updataPasteRule();
  };

  /**
   * 粘贴规则
   */
  const handlePaste = () => {
    try {
      const ruleData = localStorage.getItem('taskSet@ruleData');
      if (!ruleData) {
        throw new Error(t('dj-没有可以粘贴的规则'));
      }
      const rule = JSON.parse(ruleData);
      setType('create');
      setRuleData(rule);
      setIsOpen(true);
    } catch (error) {
      message.error(error.message);
    }
  };

  /**
   * 添加收藏
   */
  const handleCollection = () => {
    const selectedRule = ruleList.find((item: any) => item.key === selectedRuleKey);
    if (!selectedRule) {
      message.error(t('dj-请选择一个规则再进行收藏'));
      return;
    }
    setFavoriteRule(selectedRule);
    setCollectionModalIsOpen(true);
  };

  const handleRuleClick = (ruleKey: string) => {
    setSelectedRuleKey(ruleKey);
  };

  // 有多种场景会触发规则列表的刷新，这里考虑使用全局事件
  useGlobalEventEmitter(RuleManageEventType.ruleManageReloadList, ruleManageReloadList);

  // 按钮操作
  const renderShortCut = () => {
    return <>
      <a className="option-button" onClick={handleCopy2Storage}>
        <Icon type="iconfuzhi11"/>
        <span className="option-button-name">{t('dj-复制')}</span>
      </a>
      <a
        className={`option-button ${!pasteRule ? 'disable-paste-rule' : ''}`}
        onClick={handlePaste}
      >
        <Icon type="iconpaste1"/>
        <span className="option-button-name">{t('dj-粘贴')}</span>
      </a>
      <a className="option-button" onClick={handleCollection}>
        <Icon type="iconshoucang4"/>
        <span className="option-button-name">{t('dj-收藏')}</span>
      </a>
    </>
  };

  // 新增操作
  const renderAdd = () => {
    return <HidePopover.Provider value={() => handleOpenAddSelectChange(false)}>
      <Popover
        content={<RuleManageAddSelect hideShortcut={hideShortcut}/>}
        arrow={false}
        trigger="click"
        placement="bottomLeft"
        autoAdjustOverflow={false}
        open={openAddSelect}
        onOpenChange={handleOpenAddSelectChange}
        overlayClassName="search-bar-button-popover"
      >
        <div className="search-bar-button">
          <Icon type="iconshulidejiahao" />
        </div>
      </Popover>
    </HidePopover.Provider>
  };

  return (
    <div className="rule-manage-rule-list">
      <div className="search-bar">
        <Input
          className="search-bar-input"
          placeholder={t('dj-请输入规则名称或代号')}
          size="middle"
          allowClear={true}
          onChange={debounce(searchValueChange, 300)}
          suffix={<SearchOutlined style={{ color: 'rgba(0,0,0,.45)' }} />}
        />
        {
          !hideAuth ? (
            <AuthWrapper
              authParams={{
                action: 'update',
              }}
            >
              { renderAdd() }
            </AuthWrapper>
          ) : (
            renderAdd()
          )
        }
      </div>
      <div className="handle-bar">
        {
          !hideShortcut && (
            !hideAuth ? (
              <AuthWrapper
                authParams={{
                  action: 'update',
                }}
              >
                { renderShortCut() }
              </AuthWrapper>
            ) : (
              renderShortCut()
            )
          )
        }
      </div>
      <div className="rule-list">
        {!ruleManageOriginData?.isOffline && status === 'pending' && <Spin />}
        {(ruleManageOriginData?.isOffline || status !== 'pending') && showRuleList.length === 0 && (
          <div className="tab-content">
            <Empty />
          </div>
        )}
        {(ruleManageOriginData?.isOffline || status !== 'pending') &&
          showRuleList.map((rule) => {
            return (
              <RuleManageRuleListItem
                rule={rule}
                key={rule.key}
                isSelected={selectedRuleKey === rule.key}
                onSelect={handleRuleClick}
              />
            );
          })}
      </div>
      {externalAddSelectInfo && (
        <div className="custom-position-add-select" style={externalAddSelectInfo.addSelectPosition}>
          <RuleManageAddSelect
            hideShortcut={hideShortcut}
            addRuleBaseInfo={externalAddSelectInfo.addRuleBaseInfo}
            contextDataSourceName={externalAddSelectInfo.contextDataSourceName}
          />
        </div>
      )}
    </div>
  );
};

export default RuleManageRuleList;
