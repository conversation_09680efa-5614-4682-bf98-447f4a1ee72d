export interface RuleManageOriginData {
  domainType?: string; // 作业的 category
  code?: string; // 作业的 code
  applicationCode?: string; // 应用的 code
  fieldTree?: any[]; // 字段列表
  isMobile?: boolean; // 是否是移动端
  isOffline?: boolean; // 是否规则离线
  offlineRuleList?: Rule[]; // 离线规则列表，离线规则时该字段才有意义
  defaultDataSourceName?: string; // 和全局默认的数据源名称
  fieldTreeMap?: { [key: string]: any }; // 字段列表的map, 可以切换数据源的时候使用
  hideShortcut?: boolean; // 隐藏复制、粘贴、模板等快捷功能
  hideAuth?: boolean; // 隐藏权限控制
  hideRules?: any[]; // 隐藏规则列表
}

export interface Rule {
  key: string; // 规则的唯一值
  content: RuleContent; // 规则内容
  [propName: string]: any;
}

export interface RuleContent {
  key: string; // 规则类型
  [propName: string]: any;
}

// 规则管理的事件
export enum RuleManageEventType {
  ruleManageReloadList = 'ruleManageReloadList', // 触发列表组件刷新
  ruleManageReloadTemplate = 'ruleManageReloadTemplate', // 触发模板列表的刷新
}

// 如果有值说明需要打开该面板(主要是微前端或者其他外部调用)
export interface ExternalAddSelectInfo {
  addSelectPosition: React.CSSProperties; // 规则添加面板的位置
  addRuleBaseInfo?: Rule; // 添加规则的基础信息
  contextDataSourceName?: string; // 上下文数据源名称，父容器组件的数据源名称 || 全局默认的数据源名称
}
