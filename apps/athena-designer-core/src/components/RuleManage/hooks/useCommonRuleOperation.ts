import {
  useRuleManageContentModalStore,
  useRuleManageRuleListStore,
  useRuleManageStore,
} from '../store';
import { getDefaultValue } from '../utils/tools';
import { Modal, message } from 'antd';
import { t } from 'i18next';
import { deleteRule as deleteRuleApi } from '../api/request';
import { globalEventEmitter } from '@/common/hooks/useGlobalEventEmitter';
import { RuleManageEventType, Rule } from '../config/ruleManage.type';
import { merge } from 'lodash';
import { useContext } from 'react';
import { OfflineRuleListChange } from '../context';

const useCommonRuleOperation = () => {
  const { ruleManageOriginData, setContextDataSourceName } = useRuleManageStore((state) => ({
    ruleManageOriginData: state.ruleManageOriginData,
    setContextDataSourceName: state.setContextDataSourceName,
  }));

  const { setIsOpen, setType, setRuleData } = useRuleManageContentModalStore((state) => ({
    setIsOpen: state.setIsOpen,
    setType: state.setType,
    setRuleData: state.setRuleData,
  }));

  const { ruleList, setRuleList } = useRuleManageRuleListStore((state) => ({
    ruleList: state.ruleList,
    setRuleList: state.setRuleList,
  }));

  const offlineRuleListChange = useContext(OfflineRuleListChange);

  const addRule = (data: {
    ruleData: Rule;
    generateType?: 'notGenerate' | 'generate' | 'generateMerge';
    contextDataSourceName?: string;
  }) => {
    const { ruleData, generateType = 'generate', contextDataSourceName } = data;
    setType('create');
    let rule = ruleData;

    if (generateType === 'generate') {
      rule = getDefaultValue(rule.key);
    }

    if (generateType === 'generateMerge') {
      rule = merge(getDefaultValue(rule.key), rule);
    }

    setRuleData(rule);
    setContextDataSourceName(contextDataSourceName);
    setIsOpen(true);
  };

  const editRule = (ruleData: Rule, contextDataSourceName?: string) => {
    setType('edit');
    setRuleData(ruleData);
    setContextDataSourceName(contextDataSourceName);
    setIsOpen(true);
  };

  const deleteRule = (ruleData: Rule) => {
    Modal.confirm({
      content: t('dj-确认删除规则tips', { data: ruleData.content.name || '规则' }),
      style: { height: '150px' },
      okText: t('dj-确定'),
      cancelText: t('dj-取消'),
      onOk: async () => {
        try {
          // 这里有闭包，拿不到store，要实时获取
          const { isOffline } = useRuleManageStore.getState().ruleManageOriginData;
          if (isOffline) {
            message.success(t('dj-删除成功'));
            // setRuleList(ruleList.filter((item) => item.key !== ruleData.key));
            offlineRuleListChange(ruleList.filter((item) => item.key !== ruleData.key));
            return;
          }
          const { data: resData } = await deleteRuleApi(ruleData.key);
          if (resData.code === 0) {
            message.success(t('dj-删除成功'));
            globalEventEmitter.emit(RuleManageEventType.ruleManageReloadList);
          } else {
            message.error(resData.msg);
          }
        } catch (error) {
          message.error(error?.response?.data?.msg ?? error.message);
        }
      },
    });
  };

  return { addRule, editRule, deleteRule };
};

export default useCommonRuleOperation;
