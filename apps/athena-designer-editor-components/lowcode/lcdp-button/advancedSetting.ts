import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';
import { ButtonType } from './enum';
import { OtherButtonMeta } from './other-meta';

export const SaveAdvanceSetting: IPublicTypeFieldConfig = {
  type: 'group',
  title: 'dj-高级配置',
  display: 'accordion',
  condition: (target) => {
    const dslInfo = target.node?.getPropValue('dslInfo');
    return dslInfo?.type === ButtonType.BUTTON_COMBINE_SAVE_DECOUPLE;
  },
  items: [
    {
      getValue: (target) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        return dslInfo.trailingAction;
      },
      setValue: (target, value) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        if (value) {
          const trailingAction = dslInfo.trailingAction + ',close-page';
          // 避免',close-page'这种子串
          const list = trailingAction.split(',');
          if (list[0] === '') list.splice(0, 1);
          const result = list.join();
          target?.node?.setPropValue('dslInfo.trailingAction', result);
        } else {
          const acionStr = dslInfo.trailingAction;
          const actionList = acionStr.split(',');
          actionList.splice(actionList.indexOf('close-page'), 1);
          const trailingAction = actionList.join();
          target?.node?.setPropValue('dslInfo.trailingAction', trailingAction);
        }
      },
      setter: {
        componentName: 'LibSwitchSetter',
        isDynamic: false,
        props: {
          options: {
            titleProps: {
              setterTitle: 'dj-保存并关闭',
              layout: 'horizontal',
            },
            componentType: { type: 'close-page' },
            isSwitchOpen: false,
            componentProps: {
              size: 'small',
            },
          },
        },
      },
    },
    {
      getValue: (target) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        return dslInfo.trailingAction;
      },
      setValue: (target, value) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        if (value) {
          const trailingAction = dslInfo.trailingAction + ',reload-parent-page';
          const list = trailingAction.split(',');
          if (list[0] === '') list.splice(0, 1);
          const result = list.join();
          target?.node?.setPropValue('dslInfo.trailingAction', result);
        } else {
          const acionStr = dslInfo.trailingAction;
          const actionList = acionStr.split(',');
          actionList.splice(actionList.indexOf('reload-parent-page'), 1);
          const trailingAction = actionList.join();
          target?.node?.setPropValue('dslInfo.trailingAction', trailingAction);
        }
      },
      setter: {
        componentName: 'LibSwitchSetter',
        isDynamic: false,
        props: {
          options: {
            titleProps: {
              setterTitle: 'dj-保存并更新',
              tooltip: { title: 'dj-提交后触发父级页的数据更新' },
              layout: 'horizontal',
            },
            componentType: { type: 'reload-parent-page' },
            isSwitchOpen: false,
            componentProps: {
              size: 'small',
            },
          },
        },
      },
    },
    {
      getValue: (target) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        return dslInfo?.trailingAction;
      },
      setValue: (target, value) => {
        // 保存并新增和保存并刷新共用一个尾随事件
        const dslInfo = target.node?.getPropValue('dslInfo');
        if (value) {
          const trailingAction = dslInfo.trailingAction + ',reload-page';
          const list = trailingAction.split(',');
          if (list[0] === '') list.splice(0, 1);
          const result = [...new Set(list)].join();
          target?.node?.setPropValue('dslInfo.trailingAction', result);
        } else {
          const acionStr = dslInfo.trailingAction;
          const actionList = acionStr.split(',');
          actionList.splice(actionList.indexOf('reload-page'), 1);
          const trailingAction = actionList.join();
          target?.node?.setPropValue('dslInfo.trailingAction', trailingAction);
        }
      },
      setter: {
        componentName: 'LibSwitchSetter',
        isDynamic: false,
        props: {
          options: {
            titleProps: {
              setterTitle: 'dj-保存并新增',
              tooltip: {
                title: 'dj-保存成功后更新为空白页，可满足用户连续新增场景，暂不支持弹层',
              },
              layout: 'horizontal',
            },
            componentType: { type: 'reload-page' },
            isSwitchOpen: false,
            componentProps: {
              size: 'small',
            },
          },
        },
      },
    },
    {
      getValue: (target) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        return dslInfo?.trailingAction;
      },
      setValue: (target, value) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        if (value) {
          const trailingAction = dslInfo.trailingAction + ',reload-page';
          const list = trailingAction.split(',');
          if (list[0] === '') list.splice(0, 1);
          const result = [...new Set(list)].join();
          target?.node?.setPropValue('dslInfo.trailingAction', result);
        } else {
          const acionStr = dslInfo.trailingAction;
          const actionList = acionStr.split(',');
          actionList.splice(actionList.indexOf('reload-page'), 1);
          const trailingAction = actionList.join();
          target?.node?.setPropValue('dslInfo.trailingAction', trailingAction);
        }
      },
      setter: {
        componentName: 'LibSwitchSetter',
        isDynamic: false,
        props: {
          options: {
            titleProps: {
              setterTitle: 'dj-保存并刷新',
              tooltip: { title: 'dj-保存成功后刷新当前页，确保变更后的相关联数据为更新' },
              layout: 'horizontal',
            },
            componentType: { type: 'reload-page' },
            isSwitchOpen: false,
            componentProps: {
              size: 'small',
            },
          },
        },
      },
    },
    OtherButtonMeta.condition,
    { ...OtherButtonMeta.hiddenConfig },
    { ...OtherButtonMeta.confirm },
  ],
};

export const DeleteAdvanceSetting: IPublicTypeFieldConfig = {
  type: 'group',
  title: 'dj-高级配置',
  display: 'accordion',
  condition: (target) => {
    const dslInfo = target.node?.getPropValue('dslInfo');
    return dslInfo?.type === ButtonType.BUTTON_DATA_DELETE_DECOUPLE;
  },
  items: [
    {
      getValue: (target) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        return dslInfo?.trailingAction;
      },
      setValue: (target, value) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        if (value) {
          const trailingAction = dslInfo.trailingAction + ',close-page';
          const list = trailingAction.split(',');
          if (list[0] === '') list.splice(0, 1);
          const result = list.join();
          target?.node?.setPropValue('dslInfo.trailingAction', result);
        } else {
          const acionStr = dslInfo.trailingAction;
          const actionList = acionStr.split(',');
          actionList.splice(actionList.indexOf('close-page'), 1);
          const trailingAction = actionList.join();
          target?.node?.setPropValue('dslInfo.trailingAction', trailingAction);
        }
      },
      setter: {
        componentName: 'LibSwitchSetter',
        isDynamic: false,
        props: {
          options: {
            titleProps: {
              setterTitle: 'dj-删除并关闭',
              layout: 'horizontal',
            },
            componentType: { type: 'close-page' },
            isSwitchOpen: false,
            componentProps: {
              size: 'small',
            },
          },
        },
      },
    },
    {
      getValue: (target) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        return dslInfo?.trailingAction;
      },
      setValue: (target, value) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        if (value) {
          const trailingAction = dslInfo.trailingAction + ',reload-parent-page';
          const list = trailingAction.split(',');
          if (list[0] === '') list.splice(0, 1);
          const result = list.join();
          target?.node?.setPropValue('dslInfo.trailingAction', result);
        } else {
          const acionStr = dslInfo.trailingAction;
          const actionList = acionStr.split(',');
          actionList.splice(actionList.indexOf('reload-parent-page'), 1);
          const trailingAction = actionList.join();
          target?.node?.setPropValue('dslInfo.trailingAction', trailingAction);
        }
      },
      setter: {
        componentName: 'LibSwitchSetter',
        isDynamic: false,
        props: {
          options: {
            titleProps: {
              setterTitle: 'dj-删除并更新',
              tooltip: { title: 'dj-提交后触发父级页的数据更新' },
              layout: 'horizontal',
            },
            componentType: { type: 'reload-parent-page' },
            isSwitchOpen: false,
            componentProps: {
              size: 'small',
            },
          },
        },
      },
    },
    {
      getValue: (target) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        return dslInfo?.trailingAction;
      },
      setValue: (target, value) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        if (value) {
          const trailingAction = dslInfo.trailingAction + ',reload-page';
          const list = trailingAction.split(',');
          if (list[0] === '') list.splice(0, 1);
          const result = list.join();
          target?.node?.setPropValue('dslInfo.trailingAction', result);
        } else {
          const acionStr = dslInfo.trailingAction;
          const actionList = acionStr.split(',');
          actionList.splice(actionList.indexOf('reload-page'), 1);
          const trailingAction = actionList.join();
          target?.node?.setPropValue('dslInfo.trailingAction', trailingAction);
        }
      },
      setter: {
        componentName: 'LibSwitchSetter',
        isDynamic: false,
        props: {
          options: {
            titleProps: {
              setterTitle: 'dj-删除并刷新',
              tooltip: { title: 'dj-删除成功后刷新当前页，确保变更后的相关联数据为更新' },
              layout: 'horizontal',
            },
            componentType: { type: 'reload-page' },
            isSwitchOpen: false,
            componentProps: {
              size: 'small',
            },
          },
        },
      },
    },
    OtherButtonMeta.condition,
    { ...OtherButtonMeta.hiddenConfig },
    { ...OtherButtonMeta.confirm },
  ],
};

export const AddAdvanceSetting: IPublicTypeFieldConfig = {
  type: 'group',
  title: 'dj-高级配置',
  display: 'accordion',
  condition: (target) => {
    const dslInfo = target.node?.getPropValue('dslInfo');
    return [
      ButtonType.BUTTON_OPENPAGE_ADD_DECOUPLE,
      ButtonType.BUTTON_ADD_ITEM_DECOUPLE,
      ButtonType.BUTTON_DELETE_ITEM_DECOUPLE,
      ButtonType.BUTTON_DETAIL_ITEM_DECOUPLE,
      ButtonType.BUTTON_EDIT_ITEM_DECOUPLE,
    ].includes(dslInfo?.type);
  },
  items: [
    {
      name: 'dslInfo.emitConfig.dslCode',
      condition: (target) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        return [
          ButtonType.BUTTON_OPENPAGE_ADD_DECOUPLE,
          ButtonType.BUTTON_DETAIL_ITEM_DECOUPLE,
          ButtonType.BUTTON_EDIT_ITEM_DECOUPLE,
        ].includes(dslInfo?.type);
      },
      getValue: (target) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        return dslInfo?.emitConfig.dslCode;
      },
      setValue: (target, value) => {
        target?.node?.setPropValue('dslInfo.emitConfig.dslCode', value);
      },
      setter: {
        componentName: 'LcpLibTargetPage',
        isDynamic: false,
        props: {
          options: {
            titleProps: {
              setterTitle: 'dj-目标页面',
              layout: 'horizontal',
            },
          },
        },
      },
    },
    {
      name: 'dslInfo.condition',
      getValue: (target) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        return dslInfo?.condition;
      },
      setter: {
        componentName: 'LcdpInputEditorSetter',
        isDynamic: false,
        props: {
          options: {
            titleProps: {
              setterTitle: '条件',
              layout: 'horizontal',
            },
          },
          editorOptions: {
            title: 'dj-设置',
            type: 'json',
          },
        },
      },
    },
    { ...OtherButtonMeta.hiddenConfig },
    {
      name: 'dslInfo.operation.operateScript',
      getValue: (target) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        return dslInfo?.operation?.operateScript;
      },
      setter: {
        componentName: 'LcdpInputEditorSetter',
        isDynamic: false,
        props: {
          options: {
            titleProps: {
              setterTitle: 'dj-执行脚本',
              layout: 'horizontal',
            },
          },
          editorOptions: {
            title: 'dj-设置',
            type: 'javascript',
          },
          showType: 'textarea',
        },
      },
    },
  ],
};
