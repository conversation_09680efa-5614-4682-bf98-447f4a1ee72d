import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';
import { ButtonType } from './enum';
import { handleInnerActions, handleInnerActionsRevert } from './tools';
import { CommonButtonTypeSet } from './constant';
import { AddAdvanceSetting, DeleteAdvanceSetting, SaveAdvanceSetting } from './advancedSetting';

export const BaseButtonMeta: IPublicTypeFieldConfig[] = [
  {
    title: 'dj-内容',
    type: 'group',
    display: 'accordion',
    items: [
      {
        name: 'dslInfo.lang.title',
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.lang?.title;
        },
        setValue: (target, value) => {
          target?.node?.setPropValue('dslInfo.title', value?.zh_CN);
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-按钮文本',
                layout: 'horizontal',
              },
              formItemRules: [{ required: true }],
              componentType: 'lang',
            },
          },
        },
      },
      {
        name: 'dslInfo.iconConfig.name',
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.iconConfig;
        },
        setValue: (target, value) => {
          const { iconType, position } = value;
          // 新的图片组件必须选择图片位置，不用给默认值
          target?.node?.setPropValue('dslInfo.iconConfig.name', iconType);
          target?.node?.setPropValue('dslInfo.iconConfig.position', position);
        },
        setter: {
          isDynamic: false,
          componentName: 'LcdpIconSetter',
          props: {
            options: {
              titleProps: {
                setterTitle: '',
              },
            },
            allowClear: true,
          },
        },
      },
      {
        name: 'dslInfo',
        condition: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return !CommonButtonTypeSet.has(dslInfo?.type);
        },
        setValue: (target, value) => {
          target.node?.setPropValue('dslInfo', value);
        },
        setter: {
          componentName: 'LcdpButtonTypeSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-操作',
                layout: 'horizontal',
              },
            },
          },
        },
      },
      {
        title: '保存API',
        type: 'group',
        display: 'accordion',
        condition: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.type === ButtonType.BUTTON_COMBINE_SAVE_DECOUPLE;
        },
        items: [
          // 新增API
          {
            // name: 'dslInfo.addDataConnectorId',
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return {
                dataConnectorId: dslInfo?.action?.combineAttachActions?.[0].dataConnectorId,
                clear: false,
              };
            },
            setValue: (target, value) => {
              const { dataConnectorId } = value;
              const combineAttachActions = [
                {
                  dataSourceType: 'dataConnector',
                  dataConnectorId: dataConnectorId,
                  title: '新建',
                  lang: {
                    title: {
                      zh_CN: '新建',
                      zh_TW: '新建',
                      en_US: 'Create',
                    },
                  },
                },
                {
                  dataSourceType: 'dataConnector',
                  dataConnectorId: '',
                  title: '保存',
                  lang: {
                    title: {
                      zh_CN: '保存',
                      zh_TW: '存檔',
                      en_US: 'Save',
                    },
                  },
                },
              ];

              target?.node?.setPropValue(
                'dslInfo.action.combineAttachActions',
                combineAttachActions,
              );
            },
            setter: {
              componentName: 'LcdpDataConnectorSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-新增API',
                    layout: 'horizontal',
                  },
                },
              },
            },
          },
          // 更新API
          {
            // name: 'dslInfo.updateDataConnectorId',
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return {
                dataConnectorId: dslInfo?.action?.combineAttachActions?.[1].dataConnectorId,
                clear: false,
              };
            },
            setValue: (target, value) => {
              const { dataConnectorId } = value;
              const combineAttachActions = [
                {
                  dataSourceType: 'dataConnector',
                  dataConnectorId: '',
                  title: '新建',
                  lang: {
                    title: {
                      zh_CN: '新建',
                      zh_TW: '新建',
                      en_US: 'Create',
                    },
                  },
                },
                {
                  dataSourceType: 'dataConnector',
                  dataConnectorId: dataConnectorId,
                  title: '保存',
                  lang: {
                    title: {
                      zh_CN: '保存',
                      zh_TW: '存檔',
                      en_US: 'Save',
                    },
                  },
                },
              ];
              target?.node?.setPropValue(
                'dslInfo.action.combineAttachActions',
                combineAttachActions,
              );
            },
            setter: {
              componentName: 'LcdpDataConnectorSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-更新API',
                    layout: 'horizontal',
                  },
                },
              },
            },
          },
        ],
      },
      {
        // name: 'dslInfo.deleteDataConnectorId',
        condition: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.type === ButtonType.BUTTON_DATA_DELETE_DECOUPLE;
        },
        getValue: (target) => {
          const dslInfo = target?.node?.getPropValue('dslInfo');
          return {
            dataConnectorId: dslInfo?.action?.dataConnectorId,
            clear: false,
          };
        },
        setValue: (target, value) => {
          const { dataConnectorId } = value;
          target?.node?.setPropValue('dslInfo.action.dataConnectorId', dataConnectorId);
        },
        setter: {
          componentName: 'LcdpDataConnectorSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-删除API',
                layout: 'horizontal',
              },
            },
          },
        },
      },
      {
        name: 'dslInfo.action.submitType.isBatch',
        condition: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return [
            ButtonType.BUTTON_DATA_DELETE_DECOUPLE,
            ButtonType.BUTTON_COMBINE_SAVE_DECOUPLE,
          ].includes(dslInfo?.type);
        },
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.action?.submitType?.isBatch;
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-是否分批',
                layout: 'horizontal',
              },
              componentType: 'switch',
              componentProps: {
                size: 'small',
              },
            },
          },
        },
      },
      {
        name: 'dslInfo.action.submitType.submitAll',
        condition: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return [
            ButtonType.BUTTON_DATA_DELETE_DECOUPLE,
            ButtonType.BUTTON_COMBINE_SAVE_DECOUPLE,
          ].includes(dslInfo?.type);
        },
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.action?.submitType?.submitAll;
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-是否提交所有数据',
                layout: 'horizontal',
              },
              componentType: 'switch',
              componentProps: {
                size: 'small',
              },
            },
          },
        },
      },
    ],
  },
  {
    title: 'dj-关联字段',
    type: 'group',
    display: 'accordion',
    items: [
      {
        getValue: (target) => {
          const dslInfo = target?.node?.getPropValue('dslInfo');
          const { schema, path } = dslInfo;
          return { schema, path };
        },
        setValue: (target, value) => {
          const dslInfo = target?.node?.getPropValue('dslInfo');
          const { schema, path } = value;
          target?.node?.setPropValue('dslInfo', {
            ...(dslInfo ?? {}),
            schema,
            path,
          });
        },
        setter: {
          componentName: 'AthSelectAssociationFieldSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-关联字段',
                tooltip: { title: 'dj-按钮title关联字段' },
                layout: 'horizontal',
              },
            },
          },
        },
      },
      {
        name: 'dslInfo.schema',
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.schema;
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'schema',
                layout: 'horizontal',
              },
            },
          },
        },
      },
      {
        name: 'dslInfo.path',
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.path;
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'path',
                layout: 'horizontal',
              },
            },
          },
        },
      },
      {
        getValue: (target) => {
          const dslInfo = target?.node?.getPropValue('dslInfo');
          const { targetSchema, targetPath } = dslInfo;
          return { schema: targetSchema, path: targetPath };
        },
        setValue: (target, value) => {
          const { schema, path } = value;
          target?.node?.setPropValue('dslInfo.targetSchema', schema);
          target?.node?.setPropValue('dslInfo.targetPath', path);
        },
        setter: {
          componentName: 'AthSelectAssociationFieldSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-目标关联字段',
                tooltip: { title: 'dj-按钮指向数据关联字段' },
                layout: 'horizontal',
              },
            },
          },
        },
      },
      {
        name: 'dslInfo.targetSchema',
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.targetSchema;
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'targetSchema',
                layout: 'horizontal',
              },
              formItemRules: [
                {
                  required: true,
                  message: '不可为空',
                },
              ],
            },
          },
        },
      },
      {
        name: 'dslInfo.targetPath',
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.targetPath;
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'targetPath',
                layout: 'horizontal',
              },
            },
          },
        },
      },
    ],
  },
  {
    type: 'group',
    title: 'dj-基础设置',
    display: 'accordion',
    items: [
      {
        name: 'dslInfo.styleMode',
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.styleMode;
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-按钮类型',
                layout: 'horizontal',
              },
              // componentType: 'radioGroup',
              componentType: 'select',
              componentProps: {
                block: true,
                buttonStyle: 'outline',
                optionType: 'button',
                options: [
                  {
                    label: '主按钮',
                    value: 'primary',
                    tooltip: 'dj-主按钮',
                  },
                  {
                    label: '默认按钮',
                    value: 'default',
                    tooltip: 'dj-默认按钮',
                  },
                  {
                    label: '虚线按钮',
                    value: 'dashed',
                    tooltip: 'dj-虚线按钮',
                  },
                  {
                    label: '文本按钮',
                    value: 'text',
                    tooltip: 'dj-文本按钮',
                  },
                  {
                    label: '链接按钮',
                    value: 'link',
                    tooltip: 'dj-链接按钮',
                  },
                ],
              },
            },
          },
        },
      },
      {
        name: 'dslInfo.size',
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.size;
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-大小',
                layout: 'horizontal',
              },
              componentType: 'radioGroup',
              componentProps: {
                block: true,
                buttonStyle: 'outline',
                optionType: 'button',
                options: [
                  {
                    label: '大',
                    value: 'large',
                    tooltip: 'dj-大',
                  },
                  {
                    label: '中',
                    value: 'default',
                    tooltip: 'dj-默认',
                  },
                  {
                    label: '小',
                    value: 'small',
                    tooltip: 'dj-小',
                  },
                ],
              },
            },
          },
        },
      },
      {
        name: 'dslInfo.ghost',
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.ghost;
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-幽灵按钮',
                layout: 'horizontal',
              },
              componentType: 'switch',
              componentProps: {
                size: 'small',
              },
            },
          },
        },
      },
      {
        name: 'dslInfo.block',
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.block;
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-块状按钮',
                layout: 'horizontal',
              },
              componentType: 'switch',
              componentProps: {
                size: 'small',
              },
            },
          },
        },
      },
      {
        name: 'dslInfo.danger',
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.danger;
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-危险按钮',
                layout: 'horizontal',
              },
              componentType: 'switch',
              componentProps: {
                size: 'small',
              },
            },
          },
        },
      },
      {
        name: 'dslInfo.disabled',
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.disabled;
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-禁用',
                layout: 'horizontal',
              },
              componentType: 'switch',
              componentProps: {
                size: 'small',
              },
            },
          },
        },
      },
      // {
      //   name: 'dslInfo.enableAuthKey',
      //   setValue: (target, value) => {
      //     const dslInfo = target.node?.getPropValue('dslInfo');
      //     const newDslInfo = {
      //       ...(dslInfo ?? {}),
      //       enableAuthKey: value,
      //     };
      //     const getConfig = getAppHelperUtils(target.node, 'getConfigByKey');
      //     const extraData = getConfig('AthExtraData');
      //     const iamCondition = extraData?.iamCondition ?? [];
      //     if (!value) {
      //       newDslInfo.authKey = null;
      //     } else {
      //       const type = dslInfo?.type;
      //       const defaultAuthKey = DynamicButtonTypeToPermissionTypeMap.get(type);
      //       if (
      //         defaultAuthKey &&
      //         iamCondition?.some((condition) => condition.key === defaultAuthKey)
      //       ) {
      //         newDslInfo.authKey = defaultAuthKey;
      //       }
      //     }
      //     target.node?.setPropValue('dslInfo', newDslInfo);
      //   },
      //   setter: {
      //     componentName: 'AthCommonSetter',
      //     isDynamic: false,
      //     props: {
      //       options: {
      //         titleProps: {
      //           setterTitle: 'dj-权限控制',
      //           layout: 'horizontal',
      //           tooltip: {
      //             title: 'dj-开启权限控制，可在鼎捷云控制台进行功能权限管理',
      //           },
      //         },
      //         componentType: 'switch',
      //         componentProps: {
      //           size: 'small',
      //         },
      //       },
      //     },
      //   },
      // },
      // {
      //   name: 'dslInfo.authKey',
      //   condition: (target) => {
      //     const dslInfo = target.node?.getPropValue('dslInfo');
      //     return !!dslInfo?.enableAuthKey;
      //   },
      //   getValue: (target) => {
      //     const dslInfo = target.node?.getPropValue('dslInfo');
      //     return dslInfo?.authKey;
      //   },
      //   setter: {
      //     componentName: 'LcdpPermissionSetter',
      //     isDynamic: false,
      //     props: {
      //       options: {
      //         titleProps: {
      //           setterTitle: 'dj-权限项',
      //           tooltip: {
      //             title: 'dj-权限项标识，即该操作在鼎捷云控制台的功能名称',
      //           },
      //         },
      //       },
      //     },
      //   },
      // },
      {
        name: 'dslInfo.showLoading',
        condition: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.type === ButtonType.BUTTON_DECOUPLE;
        },
        setValue: (target, value) => {
          target.node?.setPropValue('dslInfo.async', value);
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-加载态',
                layout: 'horizontal',
                tooltip: {
                  title: 'dj-开启后用户操作页面会遮罩加载态',
                },
              },
              componentType: 'switch',
              componentProps: {
                size: 'small',
              },
            },
          },
        },
      },
      {
        name: 'dslInfo.debounce',
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.debounce;
        },
        setValue: (target, value) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          target.node?.setPropValue('dslInfo', {
            ...(dslInfo ?? {}),
            debounce: value,
          });
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-防抖',
                layout: 'horizontal',
              },
              componentType: 'switch',
              componentProps: {
                size: 'small',
              },
            },
          },
        },
      },
      {
        name: 'dslInfo.debounceTime',
        condition: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return !!dslInfo?.debounce;
        },
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.debounceTime;
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-防抖时间(ms)',
              },
              componentType: 'number',
            },
          },
        },
      },
    ],
  },
  { ...AddAdvanceSetting },
  { ...DeleteAdvanceSetting },
  { ...SaveAdvanceSetting },
  {
    title: 'dj-全局设置',
    type: 'group',
    display: 'accordion',
    items: [
      {
        setter: {
          componentName: 'AthMonacoEditorSetter',
          isDynamic: false,
          props: {
            options: {
              showText: '配置',
              monacoEditorProps: {
                type: 'json',
                title: '配置</>',
              },
            },
          },
        },
        getValue: (target) => {
          const dslInfo = target?.node?.getPropValue('dslInfo');
          let type = dslInfo?.type;
          if (
            type === ButtonType.BUTTON_SUB_PAGE_SAVE &&
            dslInfo?.trailingAction === 'close-page'
          ) {
            type = ButtonType.BUTTON_DATA_SAVE;
          }
          if (dslInfo?.action) {
            dslInfo.action = handleInnerActions(dslInfo.action);
          }
          return {
            ...dslInfo,
            type,
          };
        },
        setValue: (target, value) => {
          let type = value?.type;
          if (
            value?.type === ButtonType.BUTTON_DATA_SAVE &&
            value?.trailingAction === 'close-page'
          ) {
            type = ButtonType.BUTTON_SUB_PAGE_SAVE;
          }
          if (value?.action) {
            value.action = handleInnerActionsRevert(value.action);
          }
          const actualValue = {
            ...(value ?? {}),
            type,
          };
          target.node?.setPropValue('dslInfo', actualValue);
        },
      },
    ],
  },
];
