import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType } from '../common/common.config';
import { BaseButtonMeta } from './base-meta';
import { ButtonSizeType, ButtonStyleMode, ButtonType } from './enum';
import { envParams } from '@/env';

const ButtonDecoupleMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.BUTTON_DECOUPLE,
  title: 'dj-按钮',
  group: 'dj-标准组件',
  category: 'dj-按钮组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'Button',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [...BaseButtonMeta],
    component: {
      isContainer: false,
    },
    advanced: {},
  },
};

const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-通用按钮',
    // TODO 按钮图标的链接
    screenshot: ``,
    schema: {
      componentName: AthenaComponentType.BUTTON_DECOUPLE,
      title: 'dj-通用按钮',
      props: {
        dslInfo: {
          id: '',
          title: '通用',
          type: ButtonType.BUTTON_DECOUPLE,
          lang: {
            title: {
              zh_CN: '通用',
              zh_TW: '通用',
              en_US: 'Universal',
            },
          },
          styleMode: ButtonStyleMode.PRIMARY,
          size: ButtonSizeType.LARGE,
          disabled: false,
          ghost: false,
          danger: false,
          block: false,
          debounce: false,
          debounceTime: 300,
        },
      },
    },
  },
  {
    title: 'dj-功能按钮',
    screenshot: ``,
    schema: {
      componentName: AthenaComponentType.BUTTON_DECOUPLE,
      title: 'dj-功能按钮',
      props: {
        dslInfo: {
          id: '',
          type: ButtonType.BUTTON_COMBINE_SAVE_DECOUPLE,
          title: '保存',
          lang: {
            title: {
              zh_CN: '保存',
              zh_TW: '保存',
              en_US: 'Save',
            },
          },
          styleMode: ButtonStyleMode.DEFAULT,
          size: ButtonSizeType.LARGE,
          disabled: false,
          ghost: false,
          danger: false,
          block: false,
          debounce: false,
          debounceTime: 300,
          action: {
            submitType: {
              schema: '',
              isBatch: false,
            },
            trackCode: 'SUBMIT',
            type: '',
            actionType: 'basic-data-combine-save',
            combineAttachActions: [
              {
                dataSourceType: 'dataConnector',
                dataConnectorId: '',
                title: '新建',
                lang: {
                  title: {
                    zh_CN: '新建',
                    zh_TW: '新建',
                    en_US: 'Create',
                  },
                },
              },
              {
                dataSourceType: 'dataConnector',
                dataConnectorId: '',
                title: '保存',
                lang: {
                  title: {
                    zh_CN: '保存',
                    zh_TW: '存檔',
                    en_US: 'Save',
                  },
                },
              },
            ],
          },
          trailingAction: 'reload-parent-page',
        },
      },
    },
  },
];

export default {
  ...ButtonDecoupleMeta,
  snippets,
};
