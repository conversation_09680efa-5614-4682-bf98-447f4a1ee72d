import {
  IPublicModelSettingField,
  IPublicTypeComponentMetadata,
  IPublicTypeSnippet,
} from '@alilc/lowcode-types';

import {
  commonAthMonacoEditorSetter,
  commonDataSourceNamesSetter,
  commonDataConnectorSetter,
} from '../common/common-meta-info.config';
import { AthenaComponentType, childWhitelistMap, LcdpComponentType } from '../common/common.config';
import { envParams } from '@/env';
import AthIconComponentMeta from '../ath-modal-icon/meta';
import FooterButtonGroupMeta from '../footer-button-group/meta';

const LcdpAthModalMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.MODAL,
  title: 'dj-弹窗',
  group: 'dj-标准组件',
  category: 'dj-容器组件',
  docUrl: '',
  screenshot: '',
  icon: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'AthModal',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonDataSourceNamesSetter },
      { ...commonDataConnectorSetter },
      {
        name: 'dslInfo.lang.title',
        setValue: (target, value) => {
          target?.node?.setPropValue('dslInfo.title', value['zh_CN']);
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-标题',
              },
              componentType: 'lang',
            },
          },
        },
      },
      {
        name: 'modalType',
        condition: (target: IPublicModelSettingField) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return !!dslInfo?.isGrid;
        },
        getValue: (target) => {
          const { modalType } = target.node?.getPropValue('dslInfo') ?? {};
          return modalType;
        },
        setValue: (target, value) => {
          target.node?.setPropValue('dslInfo.modalType', value);
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-前缀图标',
                layout: 'horizontal',
              },
              componentType: 'select',
              componentProps: {
                options: [
                  { label: 'dj-常规', value: 'confirm' },
                  { label: 'dj-警告', value: 'warning' },
                  { label: 'dj-成功', value: 'success' },
                  { label: 'dj-失败', value: 'failure' },
                ],
              },
            },
          },
        },
      },
      {
        name: 'dslInfo.lang.modalContent',
        condition: (target: IPublicModelSettingField) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return !!dslInfo?.iscontent;
        },
        setValue: (target, value) => {
          target?.node?.setPropValue('dslInfo.modalContent', value['zh_CN']);
        },
        setter: {
          isDynamic: false,
          componentName: 'AthCommonSetter',
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-文本内容',
                tooltip: { title: 'dj-固定文本内容，未选择关联字段时生效' },
              },
              layout: 'vertical',
              componentType: 'lang',
              componentProps: {
                isTextArea: true,
              },
            },
          },
        },
      },
      {
        name: 'dslInfo.fontIcon',
        condition: (target: IPublicModelSettingField) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return !!dslInfo?.isFontIcon;
        },
        setter: {
          componentName: 'AthCommonSetter',
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-前缀图标',
                layout: 'horizontal',
              },
              componentType: 'switch',
              componentProps: {
                size: 'small',
              },
            },
          },
        },
        defaultValue: false,
      },
      // 配置图标
      {
        type: 'field',
        condition: (target: IPublicModelSettingField) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return !!dslInfo?.isFontIcon;
        },
        extraProps: {
          display: 'accordion',
        },
        setter: {
          isDynamic: false,
          componentName: 'LcdpOutletModalIconSetter',
           props: (target) => {
            // 这里 target 是 IPublicModelSettingField
            const dslInfo = target.node?.getPropValue('dslInfo') || {};
            return {
              componentType: AthenaComponentType.ICON,
              fontIcon: !!dslInfo.fontIcon, // 传递 fontIcon 状态
            };
          },
        },
      },
      {
        name: 'dslInfo.isFooter',
        setter: {
          componentName: 'AthCommonSetter',
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-操作栏',
                layout: 'horizontal',
              },
              componentType: 'switch',
              componentProps: {
                size: 'small',
              },
            },
          },
        },
        defaultValue: true, // 是否显示操作栏
      },
      // 配置操作
       {
        type: 'field',
        extraProps: {
          display: 'accordion',
        },
        setter: {
          isDynamic: false,
          componentName: 'LcdpOutletModalFooterSetter',
           props: (target) => {
            // 这里 target 是 IPublicModelSettingField
            const dslInfo = target.node?.getPropValue('dslInfo') || {};
            return {
              componentType: AthenaComponentType.BUTTON_GROUP,
              isFooter: !!dslInfo.isFooter,
            };
          },
        },
      },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          {
            name: 'size',
            getValue: (target) => {
              const { size } = target.node?.getPropValue('dslInfo') ?? {};
              return size || 'medium';
            },
            setValue: (target, value) => {
              target.node?.setPropValue('dslInfo.size', value);
            },
            setter: {
              componentName: 'AthModalSizeSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-弹窗大小',
                  },
                  componentType: 'button-group',
                  componentProps: {
                    options: [
                      { label: 'dj-小', value: 'small' },
                      { label: 'dj-中', value: 'medium' },
                      { label: 'dj-大', value: 'large' },
                      { label: 'dj-超大', value: 'xlarge' },
                    ],
                  },
                },
              },
            },
            defaultValue: 'medium',
          },
          {
            name: 'dslInfo.isClose',
            setter: {
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-关闭图标',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
            defaultValue: true, // 关闭图标显示
          },
          {
            name: 'dslInfo.mask',
            setter: {
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-是否显示遮罩',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
            defaultValue: true, // 默认显示遮罩
          },
          {
            name: 'dslInfo.maskClosable',
            setter: {
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-点击蒙层关闭',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
            defaultValue: false, // 默认不关闭
          },
          {
            title: '',
            type: 'group',
            display: 'accordion',
            items: [
              {
                getValue: (target) => {
                  const { padding } = target.node?.getPropValue('dslInfo') ?? {};
                  const paddingList = padding?.replace(/\s+/g, ' ')?.split(' ') ?? [];
                  const [top, right, bottom, left] = paddingList;
                  return {
                    top,
                    right: right ?? top,
                    bottom: bottom ?? top,
                    left: left ?? right,
                  };
                },
                setValue: (target, value = {}) => {
                  let { top, right, bottom, left } = value;
                  const padding = [top || '24', right || '24', bottom || '24', left || '24'].join(
                    ' ',
                  );
                  target.node?.setPropValue('dslInfo.padding', padding);
                },
                setter: {
                  componentName: 'LcdpPaddingSetter',
                  isDynamic: false,
                  props: {
                    options: {
                      titleProps: {
                        setterTitle: 'dj-边距',
                      },
                    },
                  },
                },
              },
            ],
          },
        ],
      },
      { ...commonAthMonacoEditorSetter },
      {
        title: 'dj-操作设置（关联属性）',
        display: 'accordion',
        getValue: (target) => {
          const dslInfo = target?.node?.getPropValue('dslInfo');
          return dslInfo;
        },
        setter: {
          isDynamic: false,
          componentName: 'LcdpTableOperationSetter',
          props: {
            componentProps: {
              allTitle: 'dj-弹窗操作',
              showAll: true, // 是否整体操作
            },
          },
        },
      },
    ],
    component: {
      isContainer: true,
      nestingRule: {
        childWhitelist: childWhitelistMap.get(AthenaComponentType.MODAL) ?? [],
      },
    },
    advanced: {
      initialChildren: [
        {
          ...FooterButtonGroupMeta.snippets[0].schema,
          children: [
            {
              componentName: AthenaComponentType.BUTTON_GROUP,
              title: 'dj-按钮组',
              props: {
                dslInfo: {
                  id: '',
                  type: AthenaComponentType.BUTTON_GROUP,
                  headerName: 'dj-按钮组',
                  verticalAlignment: 'flex-end',
                  gap: '12px',
                  lang: {
                    headerName: {
                      zh_CN: '按钮组',
                      zh_TW: '按鈕組',
                      en_US: 'Button Group',
                    },
                  },
                  moreButtonConfig: {
                    enable: false,
                  },
                  group: [],
                },
              },
              children: [
                {
                  componentName: AthenaComponentType.BUTTON_DECOUPLE,
                  title: 'dj-确认',
                  props: {
                    dslInfo: {
                      id: '',
                      type: AthenaComponentType.BUTTON_DECOUPLE,
                      title: 'dj-确认',
                      styleMode: 'primary',
                      size: 'large',
                      async: true,
                      showLoading: true,
                      action: {
                        title: 'dj-确认',
                        actionType: 'basic-data-combine-save',
                        category: 'ESP',
                        type: 'COMBINE',
                        id: 'save-button',
                        dispatch: false,
                        defaultAction: true,
                        submitType: {
                          isBatch: true,
                          schema: 'work_report_info',
                        },
                        combineActions: [],
                      },
                    },
                  },
                },
                {
                  componentName: AthenaComponentType.BUTTON_DECOUPLE,
                  title: 'dj-取消',
                  props: {
                    dslInfo: {
                      id: '',
                      type: AthenaComponentType.BUTTON_DECOUPLE,
                      title: 'dj-取消',
                      styleMode: '',
                      size: 'large',
                      async: true,
                      showLoading: true,
                      action: {
                        title: 'dj-取消',
                        actionType: 'basic-data-combine-save',
                        category: 'ESP',
                        type: 'COMBINE',
                        id: 'save-button',
                        dispatch: false,
                        defaultAction: true,
                        submitType: {
                          isBatch: true,
                          schema: 'work_report_info',
                        },
                        combineActions: [],
                      },
                    },
                  },
                },
              ],
            },
          ]
        },
        {
          componentName: AthenaComponentType.ICON,
          props: AthIconComponentMeta.snippets[0].schema.props,
        },
      ],
      callbacks: {
        onNodeAdd: (addedNode, currentNode) => {},
      },
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-弹窗',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/ACTIVITY_TITLE.svg`,
    schema: {
      componentName: AthenaComponentType.MODAL,
      props: {
        dslInfo: {
          id: '',
          title: 'dj-弹窗',
          modalType: 'customDsl',
          fontIcon: false,
          dataType: 'object',
          lang: {
            title: {
              zh_CN: '弹窗',
              en_US: 'modal',
              zh_TW: '弹窗',
            },
          },
          isGrid: false,
          iscontent: false,
          isFontIcon: true,
          isFooter: true,
          setIcon: 'dj-配置图标',
          mask: true, //是否展示遮罩层,默认显示
          isClose: true, // 是否显示关闭图标
          maskClosable: false, //点击蒙层是否允许关闭,默认false
          draggable: true, //开启拖拽
          keyboard: true, //开启键盘esc关闭弹窗
          size: 'medium', // 弹窗大小
          direction: 'ROW',
          type: AthenaComponentType.MODAL,
          schema: '',
          path: '',
          collapse: false,
        },
      },
    },
  },
  {
    title: 'dj-反馈弹窗',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/DELIVERY_REPLY_DESCRIPTION.svg`,
    schema: {
      componentName: AthenaComponentType.MODAL,
      props: {
        dslInfo: {
          id: '',
          title: 'dj-反馈弹窗',
          modalType: 'confirm',  // 确认弹窗类型
          dataType: 'object',
          lang: {
            title: {
              zh_CN: '提示',
              en_US: 'modal',
              zh_TW: '提示',
            },
            modalContent: {
              zh_CN: '文本内容显示在此',
              zh_TW: '文本內容顯示在此',
              en_US: 'The text content is displayed here',
            },
          },
          isGrid: true,
          iscontent: true,
          isFontIcon: false,
          isFooter: true,
          modalContent: 'dj-文本内容显示在此',
          mask: true, //是否展示遮罩层,默认显示
          isClose: true, // 是否显示关闭图标
          maskClosable: false, //点击蒙层是否允许关闭,默认false
          draggable: true, //开启拖拽
          keyboard: true, //开启键盘esc关闭弹窗
          size: 'medium', // 弹窗大小
          direction: 'ROW',
          type: AthenaComponentType.MODAL,
          schema: '',
          path: '',
          collapse: false,
        },
      },
    },
  },
];

export default {
  ...LcdpAthModalMeta,
  snippets,
};
