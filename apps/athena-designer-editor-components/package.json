{"name": "at<PERSON>a-designer-editor-components", "version": "0.1.6", "description": "at<PERSON>a-designer-editor-components", "main": "lib/index.js", "module": "es/index.js", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "exports": {"./*": "./*", ".": {"import": "./es/index.js", "require": "./lib/index.js"}, "./prototype": {"require": "./lowcode_lib/meta.js", "import": "./lowcode_es/meta.js"}, "./prototypeView": {"require": "./lowcode_lib/view.js", "import": "./lowcode_es/view.js"}}, "typings": "types/index.d.ts", "files": ["build/", "dist/", "lib/", "es/", "types/", "lowcode/", "lowcode_lib/", "lowcode_es/"], "scripts": {"start": "npm run dumi", "build": "build-scripts build", "lowcode:dev": "build-scripts start --config ./build.lowcode.js", "lowcode:build": "build-scripts build --config ./build.lowcode.js", "lowcode:publish": "npm run build && npm run lowcode:build && npm run dumi:build && npm publish", "f2elint-scan": "f2elint scan", "f2elint-fix": "f2elint fix", "dumi": "cross-env APP_ROOT=docs dumi dev", "dumi:build": "cross-env APP_ROOT=docs dumi build", "prepublishOnly": "npm run build && npm run lowcode:build && npm run dumi:build"}, "directories": {"test": "test"}, "keywords": ["Fusion"], "author": "lcdp", "license": "MIT", "lint-staged": {"**/*.{js,jsx,ts,tsx,vue}": "f2elint exec eslint", "**/*.{css,scss,less,acss}": "f2elint exec stylelint"}, "peerDependencies": {"moment": "latest", "react": "^16.x", "react-dom": "^16.x"}, "devDependencies": {"@alib/build-scripts": "^0.1.3", "@alifd/build-plugin-lowcode": "^0.4.0", "@alifd/theme-2": "^0.4.0", "@types/lodash": "^4.17.5", "@types/react": "^16.14.24", "@types/react-dom": "^16.9.4", "@umijs/plugin-sass": "^1.1.1", "build-plugin-component": "^1.12.1", "build-plugin-fusion": "^0.1.0", "cross-env": "^7.0.3", "dumi": "^1.1.49", "dumi-theme-default": "^1.1.24", "f2elint": "^1.2.0"}, "dependencies": {"@alifd/next": "^1.25.27", "@alilc/build-plugin-alt": "^1.3.4", "@babel/runtime": "^7.0.0", "antd": "^5.18.0", "lodash": "^4.17.21", "moment": "latest", "prop-types": "^15.5.8", "react": "^16.x", "react-dom": "^16.x"}, "acceptDependencies": {"webpack": "^4.46.x"}, "resolutions": {"webpack": "^4.46.x"}, "componentConfig": {"isComponentLibrary": true, "materialSchema": "https://unpkg.com/athena-designer-editor-components@0.1.6/build/lowcode/assets-prod.json"}, "lcMeta": {"type": "component"}, "homepage": "https://unpkg.com/athena-designer-editor-components@0.1.6/build/index.html"}