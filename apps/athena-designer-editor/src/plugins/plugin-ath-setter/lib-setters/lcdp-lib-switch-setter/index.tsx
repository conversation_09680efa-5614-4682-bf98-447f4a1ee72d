import React from 'react';
import { Switch } from 'antd';
import { CommonSetterLayoutProps } from '../../components/common-setter-layout';
import { t } from 'i18next';
import './index.scss';
export interface SwitchProps {
  value?: any;
  onChange?: (values: any) => void;
  options: {
    titleProps: CommonSetterLayoutProps;
    componentProps?: any;
    componentType?: any;
    isSwitchOpen?: boolean;
  };
}

const SwitchItem: React.FC<SwitchProps> = (props: SwitchProps) => {
  const { value, onChange, options } = props;
  let { componentProps = {}, isSwitchOpen, componentType } = options ?? {};
  const setterTitle = options?.titleProps?.setterTitle as string;
  // 所有的尾随事件
  // const allTrailingAction = ['reload-parent-page', 'reload-page', 'close-page'];
  const trailingActionList = value.split(',');
  if (trailingActionList.includes(componentType.type)) isSwitchOpen = true;

  const onValueChange = (val: boolean) => {
    onChange?.(val);
  };
  return (
    <div className="action-switch">
      <div className="title">{t(setterTitle)}</div>
      <Switch {...componentProps} defaultChecked={isSwitchOpen} onChange={onValueChange} />
    </div>
  );
};
export default SwitchItem;
