import { Converter, DslData, AthComponentType, ConvertOutput, DslSchema } from '../type';

export const LcdpModalConverter: Converter = {
  key: AthComponentType.MODAL,
  toSchema: (dsl: DslData): ConvertOutput<DslSchema, DslData> => {
    const { bodyGroup = [], footerGroup, ...dslInfo } = dsl;

    // footerGroup 转换为 动态操作
    const footerSchema = footerGroup?.map((item: DslData) => {
      return {
        ...item.queryInfo,
        type: AthComponentType.BUTTON_GROUP,
      };
    }) || [
      {
        type: AthComponentType.DYNAMIC_OPERATION,
      },
    ];

    return {
      data: {
        componentName: AthComponentType.MODAL,
        props: { dslInfo },
      },
      childrenData: [
        {
          key: 'children',
          data: [...footerSchema, ...bodyGroup],
        },
      ],
    };
  },

  toDsl: (dslSchema: DslSchema): ConvertOutput<DslData, DslSchema> => {
    const { props, children = [] } = dslSchema;

    const { footerChildren, bodyChildren, icon} = children.reduce<{
      footerChildren: any[];
      bodyChildren: any[];
      icon: any;
    }>(
      (acc, cur) => {
        if (cur.componentName === AthComponentType.FOOTER_BUTTON_GROUP) {
          acc.footerChildren.push(...cur['children']);
        } else if (cur.componentName === AthComponentType.ICON) {
          acc.icon = cur.props.dslInfo;
        } else {
          acc.bodyChildren.push(cur);
        }
        return acc;
      },
      { footerChildren: [], icon: {}, bodyChildren: [] },
    );

    return {
      data: {
        ...props.dslInfo,
        titleIcon: icon,
        type: AthComponentType.MODAL,
      },
      childrenData: [
        {
          key: 'footerGroup',
          data: footerChildren,
        },
        {
          key: 'bodyGroup',
          data: bodyChildren,
        },
      ],
    };
  },

  valid: (dsl: DslData) => {
    return true;
  },
};
