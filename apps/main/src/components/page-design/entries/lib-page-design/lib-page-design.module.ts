import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { TranslateModule } from '@ngx-translate/core';
import { NzFormModule } from 'ng-zorro-antd/form';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { LibPageDesignComponent } from './lib-page-design.component';
import { BusinessShareConsumerModule } from '../../../bussiness-components/business-share-consumer/business-share-consumer.module';
import {ExtendEditorModalModule} from "../../../bussiness-components/extend-editor-modal/extend-editor-modal.module";
import {AdIconModule} from "../../../ad-ui-components/ad-icon/ad-icon.module";

@NgModule({
  declarations: [LibPageDesignComponent],
  exports: [LibPageDesignComponent],
  imports: [
    CommonModule,
    NzSpinModule,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    BusinessShareConsumerModule,
    NzFormModule,
    ExtendEditorModalModule,
    AdIconModule,
  ],
})
export class LibPageDesignModule {}
